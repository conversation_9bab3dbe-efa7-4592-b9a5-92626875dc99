import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { User, UpdateUserData, UserRole } from '../../../domain/entities/User';
import { NotFoundError, ForbiddenError, ValidationError } from '../../../presentation/middleware/errorHandler';

export interface UpdateUserRequest {
  userId: string;
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  updateData: UpdateUserData;
}

export class UpdateUserUseCase {
  constructor(private userRepository: IUserRepository) {}

  async execute(request: UpdateUserRequest): Promise<User> {
    const { userId, requesterId, requesterRole, requesterCollegeId, requesterDepartmentId, updateData } = request;

    // Find the user to update
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Check permissions
    this.validateUpdatePermissions(
      requesterRole,
      user,
      requesterCollegeId,
      requesterDepartmentId,
      requesterId
    );

    // Validate update data based on requester role
    this.validateUpdateData(requesterRole, updateData, user);

    // Update user
    const updatedUser = await this.userRepository.update(userId, updateData);
    if (!updatedUser) {
      throw new NotFoundError('User not found');
    }

    return {
      ...updatedUser,
      password: undefined,
    };
  }

  private validateUpdatePermissions(
    requesterRole: UserRole,
    targetUser: User,
    requesterCollegeId?: string,
    requesterDepartmentId?: string,
    requesterId?: string
  ): void {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can update anyone
        break;

      case UserRole.PRINCIPAL:
        // Principal can update users in their college
        if (!requesterCollegeId || targetUser.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Principal can only update users in their college');
        }
        break;

      case UserRole.HOD:
        // HOD can update users in their department
        if (!requesterDepartmentId || targetUser.departmentId !== requesterDepartmentId) {
          throw new ForbiddenError('HOD can only update users in their department');
        }
        break;

      case UserRole.STAFF:
        // Staff can update students in their department
        if (!requesterDepartmentId || 
            targetUser.departmentId !== requesterDepartmentId || 
            targetUser.role !== UserRole.STUDENT) {
          throw new ForbiddenError('Staff can only update students in their department');
        }
        break;

      case UserRole.STUDENT:
        // Students can only update themselves
        if (targetUser.id !== requesterId) {
          throw new ForbiddenError('Students can only update their own profile');
        }
        break;

      default:
        throw new ForbiddenError('Invalid role');
    }
  }

  private validateUpdateData(requesterRole: UserRole, updateData: UpdateUserData, targetUser: User): void {
    // Students can only update limited fields
    if (requesterRole === UserRole.STUDENT) {
      const allowedFields = ['name', 'phone', 'class', 'semester'];
      const updateFields = Object.keys(updateData);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new ValidationError(`Students cannot update fields: ${invalidFields.join(', ')}`);
      }
    }

    // Staff can only update limited fields for students
    if (requesterRole === UserRole.STAFF && targetUser.role === UserRole.STUDENT) {
      const allowedFields = ['name', 'phone', 'class', 'semester', 'status'];
      const updateFields = Object.keys(updateData);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new ValidationError(`Staff cannot update fields: ${invalidFields.join(', ')}`);
      }
    }

    // Validate status changes
    if (updateData.status) {
      if (requesterRole === UserRole.STUDENT) {
        throw new ValidationError('Students cannot change their status');
      }
    }
  }
}
