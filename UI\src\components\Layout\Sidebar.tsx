import React from 'react';
import {
  LayoutDashboard,
  Users,
  GraduationCap,
  Building,
  UserCheck,
  Settings,
  LogOut,
  TreePine,
  FileText,
  BookOpen,
  Mail
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import logo from '../../assets/RMKCollegelogo.gif';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange, isOpen, onClose }) => {
  const { user, logout } = useAuth();

  const getMenuItems = () => {
    const baseItems = [
      { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard }
    ];

    switch (user?.role) {
      case 'admin':
        return [
          ...baseItems,
          { id: 'colleges', label: 'Colleges', icon: Building },
          { id: 'users', label: 'Users', icon: Users },
          { id: 'invitations', label: 'Invitations', icon: Mail },
        ];
      case 'principal':
        return [
          ...baseItems,
          { id: 'staff-management', label: 'Staff Management', icon: Users },
          { id: 'departments', label: 'Departments', icon: Building },
          { id: 'students', label: 'Students', icon: GraduationCap },
          { id: 'invitations', label: 'Invitations', icon: Mail }
        ];
      case 'hod':
        return [
          ...baseItems,
          { id: 'courses', label: 'Courses', icon: BookOpen },
          { id: 'department-staff', label: 'Department Staff', icon: Users },
          { id: 'department-students', label: 'Students', icon: GraduationCap },
          { id: 'invitations', label: 'Invitations', icon: Mail },
          // { id: 'student-requests', label: 'Student Requests', icon: UserCheck }
        ];
      case 'staff':
        return [
          ...baseItems,
          { id: 'my-students', label: 'My Students', icon: GraduationCap },
          { id: 'student-management', label: 'Student Management', icon: Users },
          { id: 'invitations', label: 'Invitations', icon: Mail },
          { id: 'student-requests', label: 'Student Requests', icon: UserCheck }
        ];
      case 'student':
        return [
          ...baseItems,
          { id: 'my-tree', label: 'My Tree', icon: TreePine },
          { id: 'guidelines', label: 'Guidelines', icon: FileText },
          { id: 'resources', label: 'Resources', icon: BookOpen }
        ];
      default:
        return baseItems;
    }
  };

  const menuItems = getMenuItems();

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 
        min-h-screen flex flex-col transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Logo Section */}
        <div className="p-4 lg:p-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 flex items-center justify-center">
              <img src={logo} />
            </div>
            <div>
              <h1 className="text-base lg:text-lg font-bold text-gray-900">One Student</h1>
              <p className="text-lg lg:text-sm font-bold text-gray-900">One Tree</p>
            </div>
            {/* Mobile Close Button */}
            <button
              onClick={onClose}
              className="lg:hidden ml-auto p-2 rounded-lg hover:bg-gray-100"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 p-3 lg:p-4 overflow-y-auto">
          <div className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeTab === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => {
                    onTabChange(item.id);
                    onClose(); // Close sidebar on mobile after selection
                  }}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${isActive
                      ? 'bg-[#29503E] text-white'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium text-sm lg:text-base">{item.label}</span>
                </button>
              );
            })}
          </div>
          <button
            onClick={logout}
            className="mt-3 w-full flex items-center space-x-3 px-4 py-2 rounded-lg text-left text-red-600 hover:bg-red-50 transition-colors"
          >
            <LogOut className="w-4 h-4" />
            <span className="text-xs lg:text-sm">Logout</span>
          </button>
        </nav>

        {/* User Profile Section */}
        <div className="p-3 lg:p-4 border-t border-gray-100">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
              <span className="text-xs lg:text-sm font-medium text-gray-600">
                {user?.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs lg:text-sm font-medium text-gray-900 truncate">
                {user?.name || 'User'}
              </p>
              <p className="text-xs text-gray-500 capitalize">
                {user?.role || 'User'}
              </p>
            </div>
          </div>

          {/* <div className="space-y-2">
            <button
              onClick={() => onTabChange('settings')}
              className={`w-full flex items-center space-x-3 px-4 py-2 rounded-lg text-left transition-colors ${activeTab === 'settings'
                  ? 'bg-gray-100 text-gray-900'
                  : 'text-gray-600 hover:bg-gray-50'
                }`}
            >
              <Settings className="w-4 h-4" />
              <span className="text-xs lg:text-sm">Settings</span>
            </button>

            <button
              onClick={logout}
              className="w-full flex items-center space-x-3 px-4 py-2 rounded-lg text-left text-red-600 hover:bg-red-50 transition-colors"
            >
              <LogOut className="w-4 h-4" />
              <span className="text-xs lg:text-sm">Logout</span>
            </button>
          </div> */}
        </div>
      </div>
    </>
  );
};

export default Sidebar;