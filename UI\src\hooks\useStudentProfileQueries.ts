import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { studentProfilesApi } from '../services/api';

// Query keys
export const studentProfileKeys = {
  all: ['studentProfiles'] as const,
  lists: () => [...studentProfileKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...studentProfileKeys.lists(), { filters }] as const,
  details: () => [...studentProfileKeys.all, 'detail'] as const,
  detail: (id: string) => [...studentProfileKeys.details(), id] as const,
};

// Hooks
export function useStudentProfiles(filters?: Record<string, string>) {
  return useQuery({
    queryKey: studentProfileKeys.list(filters || {}),
    queryFn: () => studentProfilesApi.getStudentProfiles(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useStudentProfile(id: string) {
  return useQuery({
    queryKey: studentProfileKeys.detail(id),
    queryFn: () => studentProfilesApi.getStudentProfileById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateStudentProfile() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => studentProfilesApi.createStudentProfile(data),
    onSuccess: (newProfile) => {
      // Invalidate and refetch student profile lists
      queryClient.invalidateQueries({ queryKey: studentProfileKeys.lists() });
      // Set the new profile in cache
      queryClient.setQueryData(studentProfileKeys.detail(newProfile.data.id), newProfile);
    },
  });
}

export function useUpdateStudentProfile() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      studentProfilesApi.updateStudentProfile(id, data),
    onSuccess: (updatedProfile, { id }) => {
      // Invalidate and refetch student profile lists
      queryClient.invalidateQueries({ queryKey: studentProfileKeys.lists() });
      // Update the specific profile in cache
      queryClient.setQueryData(studentProfileKeys.detail(id), updatedProfile);
    },
  });
}

export function useDeleteStudentProfile() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => studentProfilesApi.deleteStudentProfile(id),
    onSuccess: (_, id) => {
      // Invalidate and refetch student profile lists
      queryClient.invalidateQueries({ queryKey: studentProfileKeys.lists() });
      // Remove the specific profile from cache
      queryClient.removeQueries({ queryKey: studentProfileKeys.detail(id) });
    },
  });
}
