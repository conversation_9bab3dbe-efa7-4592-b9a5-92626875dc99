import { Request, Response } from 'express';
import { CreateDepartmentUseCase } from '../../application/usecases/department/CreateDepartmentUseCase';
import { DepartmentRepository } from '../../infrastructure/repositories/DepartmentRepository';
import { CollegeRepository } from '../../infrastructure/repositories/CollegeRepository';
import { UserRepository } from '../../infrastructure/repositories/UserRepository';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

export class DepartmentController {
  private createDepartmentUseCase: CreateDepartmentUseCase;
  private departmentRepository: DepartmentRepository;

  constructor() {
    const departmentRepository = new DepartmentRepository();
    const collegeRepository = new CollegeRepository();
    const userRepository = new UserRepository();

    this.departmentRepository = departmentRepository;
    this.createDepartmentUseCase = new CreateDepartmentUseCase(
      departmentRepository,
      collegeRepository,
      userRepository
    );
  }

  createDepartment = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { name, code, collegeId, established } = req.body;

    // Automatically set the current user as HOD if they are an HOD role
    let hodId = null;
    if (req.user!.role === 'hod') {
      hodId = req.user!.id;
    }

    const department = await this.createDepartmentUseCase.execute({
      name,
      code,
      collegeId,
      hodId,
      established,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
    });

    res.status(201).json({
      message: 'Department created successfully',
      data: department,
    });
  });

  getDepartments = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page = '1', limit = '10', search, collegeId } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let filters: any = {
      limit: parseInt(limit as string),
      offset,
      search: search as string,
    };

    // Apply role-based filtering
    switch (req.user!.role) {
      case 'admin':
        if (collegeId) {
          filters.collegeId = collegeId as string;
        }
        break;

      case 'principal':
        filters.collegeId = req.user!.collegeId;
        break;

      case 'hod':
      case 'staff':
        // HOD and staff can only see their own department
        if (!req.user!.departmentId) {
          console.log(`User ${req.user!.id} with role ${req.user!.role} has no departmentId assigned`);
          return res.json({
            message: 'Departments retrieved successfully',
            data: [],
          });
        }

        try {
          const department = await this.departmentRepository.findById(req.user!.departmentId);
          return res.json({
            message: 'Departments retrieved successfully',
            data: department ? [department] : [],
          });
        } catch (error) {
          console.error(`Error fetching department ${req.user!.departmentId}:`, error);
          return res.status(500).json({
            error: 'Failed to fetch department data',
            details: error instanceof Error ? error.message : 'Unknown error'
          });
        }

      default:
        return res.status(403).json({ error: 'Access denied' });
    }

    const departments = await this.departmentRepository.findAll(filters);

    res.json({
      message: 'Departments retrieved successfully',
      data: departments,
    });
  });

  getDepartmentById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const department = await this.departmentRepository.findById(id);
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    // Check permissions
    const canAccess = this.canAccessDepartment(req.user!, department);
    if (!canAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      message: 'Department retrieved successfully',
      data: department,
    });
  });

  updateDepartment = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;

    const department = await this.departmentRepository.findById(id);
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    // Check permissions
    const canUpdate = this.canUpdateDepartment(req.user!, department);
    if (!canUpdate) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // HODs can only update limited fields
    if (req.user!.role === 'hod') {
      const allowedFields = ['name'];
      const updateFields = Object.keys(updateData);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        return res.status(400).json({ 
          error: `HOD cannot update fields: ${invalidFields.join(', ')}` 
        });
      }
    }

    const updatedDepartment = await this.departmentRepository.update(id, updateData);

    res.json({
      message: 'Department updated successfully',
      data: updatedDepartment,
    });
  });

  deleteDepartment = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const department = await this.departmentRepository.findById(id);
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    // Check permissions
    const canDelete = 
      req.user!.role === 'admin' || 
      (req.user!.role === 'principal' && department.collegeId === req.user!.collegeId);

    if (!canDelete) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const deleted = await this.departmentRepository.delete(id);
    if (!deleted) {
      return res.status(404).json({ error: 'Department not found' });
    }

    res.json({
      message: 'Department deleted successfully',
    });
  });

  getDepartmentStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const department = await this.departmentRepository.findById(id);
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    // Check permissions
    const canAccess = this.canAccessDepartment(req.user!, department);
    if (!canAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const userRepository = new UserRepository();
    const treePlantingRepository = new (await import('../../infrastructure/repositories/TreePlantingRepository')).TreePlantingRepository();

    const stats = {
      totalUsers: await userRepository.countByDepartment(id),
      totalStudents: await userRepository.findByDepartment(id).then(users => 
        users.filter(u => u.role === 'student').length
      ),
      totalStaff: await userRepository.findByDepartment(id).then(users => 
        users.filter(u => u.role === 'staff').length
      ),
      treePlantingStats: await treePlantingRepository.getStatsByDepartment(id),
    };

    res.json({
      message: 'Department statistics retrieved successfully',
      data: stats,
    });
  });

  private canAccessDepartment(user: any, department: any): boolean {
    switch (user.role) {
      case 'admin':
        return true;
      case 'principal':
        return department.collegeId === user.collegeId;
      case 'hod':
      case 'staff':
        return user.departmentId && department.id === user.departmentId;
      default:
        return false;
    }
  }

  private canUpdateDepartment(user: any, department: any): boolean {
    switch (user.role) {
      case 'admin':
        return true;
      case 'principal':
        return department.collegeId === user.collegeId;
      case 'hod':
        return user.departmentId && department.id === user.departmentId;
      default:
        return false;
    }
  }
}
