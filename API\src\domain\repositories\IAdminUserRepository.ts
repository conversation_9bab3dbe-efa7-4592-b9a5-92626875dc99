import { AdminUser, CreateAdminUserData, UpdateAdminUserData } from '../entities/AdminUser';

export interface IAdminUserRepository {
  create(data: CreateAdminUserData): Promise<AdminUser>;
  findById(id: string): Promise<AdminUser | null>;
  findByEmail(email: string): Promise<AdminUser | null>;
  findAll(): Promise<AdminUser[]>;
  update(id: string, data: UpdateAdminUserData): Promise<AdminUser | null>;
  delete(id: string): Promise<boolean>;
}
