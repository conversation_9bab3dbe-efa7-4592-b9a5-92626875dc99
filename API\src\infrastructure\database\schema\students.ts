import { pgTable, uuid, varchar, timestamp, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const students = treedev.table('students', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  emailId: varchar('email_id', { length: 255 }).notNull().unique(),
  phone: varchar('phone_number', { length: 20 }).notNull(),
  collegeId: uuid('college_id').notNull(),
  departmentId: uuid('department_id').notNull(),
  class: varchar('class', { length: 50 }),
  semester: varchar('semester', { length: 20 }),
  rollNumber: varchar('roll_number', { length: 50 }),
  lastSeen: timestamp('last_seen'),
  createdOn: timestamp('created_on').notNull().defaultNow(),
  modifiedOn: timestamp('modified_on').notNull().defaultNow(),
  createdBy: uuid('created_by'),
  modifiedBy: uuid('modified_by'),
});

export const insertStudentSchema = createInsertSchema(students);
export const selectStudentSchema = createSelectSchema(students);

export type Student = typeof students.$inferSelect;
export type NewStudent = typeof students.$inferInsert;
