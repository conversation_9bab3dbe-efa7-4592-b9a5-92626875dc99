import { User, UserRole } from '../../domain/entities/User';
import { AdminUser } from '../../domain/entities/AdminUser';
import { Principal } from '../../domain/entities/Principal';
import { Hod } from '../../domain/entities/Hod';
import { Staff } from '../../domain/entities/Staff';
import { Student } from '../../domain/entities/Student';
import { IUserRepository } from '../../domain/repositories/IUserRepository';
import { IAdminUserRepository } from '../../domain/repositories/IAdminUserRepository';
import { IPrincipalRepository } from '../../domain/repositories/IPrincipalRepository';
import { IHodRepository } from '../../domain/repositories/IHodRepository';
import { IStaffRepository } from '../../domain/repositories/IStaffRepository';
import { IStudentRepository } from '../../domain/repositories/IStudentRepository';

export interface UserWithProfile {
  id: string;
  email: string;
  role: UserRole;
  status: string;
  lastSeen?: Date;
  createdAt: Date;
  updatedAt: Date;
  // Profile data
  name?: string;
  phone?: string;
  collegeId?: string;
  departmentId?: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export class UserProfileService {
  constructor(
    private userRepository: IUserRepository,
    private adminUserRepository: IAdminUserRepository,
    private principalRepository: IPrincipalRepository,
    private hodRepository: IHodRepository,
    private staffRepository: IStaffRepository,
    private studentRepository: IStudentRepository
  ) {}

  async getUserWithProfile(userId: string): Promise<UserWithProfile | null> {
    const user = await this.userRepository.findById(userId);
    if (!user) return null;

    const baseUser: UserWithProfile = {
      id: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
      lastSeen: user.lastSeen,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    // Get profile data based on role
    switch (user.role) {
      case UserRole.ADMIN:
        const adminProfile = await this.adminUserRepository.findByEmail(baseUser.email);
        if (adminProfile) {
          baseUser.name = adminProfile.name;
          baseUser.phone = adminProfile.phone;
        }
        break;

      case UserRole.PRINCIPAL:
        const principalProfile = await this.principalRepository.findByEmail(baseUser.email);
        if (principalProfile) {
          baseUser.name = principalProfile.name;
          baseUser.phone = principalProfile.phone;
          baseUser.collegeId = principalProfile.collegeId;
        }
        break;

      case UserRole.HOD:
        const hodProfile = await this.hodRepository.findByEmail(baseUser.email);
        if (hodProfile) {
          baseUser.name = hodProfile.name;
          baseUser.phone = hodProfile.phone;
          baseUser.collegeId = hodProfile.collegeId;
          baseUser.departmentId = hodProfile.departmentId;
        }
        break;

      case UserRole.STAFF:
        const staffProfile = await this.staffRepository.findByEmail(baseUser.email);
        if (staffProfile) {
          baseUser.name = staffProfile.name;
          baseUser.phone = staffProfile.phone;
          baseUser.collegeId = staffProfile.collegeId;
          baseUser.departmentId = staffProfile.departmentId;
        }
        break;

      case UserRole.STUDENT:
        const studentProfile = await this.studentRepository.findByEmail(baseUser.email);
        if (studentProfile) {
          baseUser.name = studentProfile.name;
          baseUser.phone = studentProfile.phone;
          baseUser.collegeId = studentProfile.collegeId;
          baseUser.departmentId = studentProfile.departmentId;
          baseUser.class = studentProfile.class;
          baseUser.semester = studentProfile.semester;
          baseUser.rollNumber = studentProfile.rollNumber;
        }
        break;
    }

    return baseUser;
  }

  async getUsersByRole(role: UserRole): Promise<UserWithProfile[]> {
    const users = await this.userRepository.findByRole(role);
    const usersWithProfiles: UserWithProfile[] = [];

    for (const user of users) {
      const userWithProfile = await this.getUserWithProfile(user.id);
      if (userWithProfile) {
        usersWithProfiles.push(userWithProfile);
      }
    }

    return usersWithProfiles;
  }

  async getUsersByCollege(collegeId: string): Promise<UserWithProfile[]> {
    const usersWithProfiles: UserWithProfile[] = [];

    // Get principals for this college
    const principals = await this.principalRepository.findByCollegeId(collegeId);
    for (const principal of principals) {
      const user = await this.userRepository.findByEmail(principal.emailId);
      if (user) {
        usersWithProfiles.push({
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
          lastSeen: user.lastSeen,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          name: principal.name,
          phone: principal.phone,
          collegeId: principal.collegeId,
        });
      }
    }

    // Get HODs for this college
    const hods = await this.hodRepository.findByCollegeId(collegeId);
    for (const hod of hods) {
      const user = await this.userRepository.findByEmail(hod.emailId);
      if (user) {
        usersWithProfiles.push({
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
          lastSeen: user.lastSeen,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          name: hod.name,
          phone: hod.phone,
          collegeId: hod.collegeId,
          departmentId: hod.departmentId,
        });
      }
    }

    // Get staff for this college
    const staff = await this.staffRepository.findByCollegeId(collegeId);
    for (const staffMember of staff) {
      const user = await this.userRepository.findByEmail(staffMember.emailId);
      if (user) {
        usersWithProfiles.push({
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
          lastSeen: user.lastSeen,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          name: staffMember.name,
          phone: staffMember.phone,
          collegeId: staffMember.collegeId,
          departmentId: staffMember.departmentId,
        });
      }
    }

    // Get students for this college
    const students = await this.studentRepository.findByCollegeId(collegeId);
    for (const student of students) {
      const user = await this.userRepository.findByEmail(student.emailId);
      if (user) {
        usersWithProfiles.push({
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
          lastSeen: user.lastSeen,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          name: student.name,
          phone: student.phone,
          collegeId: student.collegeId,
          departmentId: student.departmentId,
          class: student.class,
          semester: student.semester,
          rollNumber: student.rollNumber,
          createdBy: student.createdBy,
          modifiedBy: student.modifiedBy,
        });
      }
    }

    return usersWithProfiles;
  }

  async getUsersByDepartment(departmentId: string): Promise<UserWithProfile[]> {
    const usersWithProfiles: UserWithProfile[] = [];

    // Get HODs for this department
    const hods = await this.hodRepository.findByDepartmentId(departmentId);
    for (const hod of hods) {
      const user = await this.userRepository.findByEmail(hod.emailId);
      if (user) {
        usersWithProfiles.push({
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
          lastSeen: user.lastSeen,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          name: hod.name,
          phone: hod.phone,
          collegeId: hod.collegeId,
          departmentId: hod.departmentId,
        });
      }
    }

    // Get staff for this department
    const staff = await this.staffRepository.findByDepartmentId(departmentId);
    for (const staffMember of staff) {
      const user = await this.userRepository.findByEmail(staffMember.emailId);
      if (user) {
        usersWithProfiles.push({
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
          lastSeen: user.lastSeen,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          name: staffMember.name,
          phone: staffMember.phone,
          collegeId: staffMember.collegeId,
          departmentId: staffMember.departmentId,
        });
      }
    }

    // Get students for this department
    const students = await this.studentRepository.findByDepartmentId(departmentId);
    for (const student of students) {
      const user = await this.userRepository.findByEmail(student.emailId);
      if (user) {
        usersWithProfiles.push({
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
          lastSeen: user.lastSeen,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          name: student.name,
          phone: student.phone,
          collegeId: student.collegeId,
          departmentId: student.departmentId,
          class: student.class,
          semester: student.semester,
          rollNumber: student.rollNumber,
          createdBy: student.createdBy,
          modifiedBy: student.modifiedBy,
        });
      }
    }

    return usersWithProfiles;
  }
}
