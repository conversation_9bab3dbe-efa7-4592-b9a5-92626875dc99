-- Migration: Restructure user tables to separate role-specific data
-- This migration creates new role-specific tables and migrates existing data

-- Create new role-specific tables
CREATE TABLE IF NOT EXISTS "treedev"."admin_users" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
    "name" varchar(255) NOT NULL,
    "email_id" varchar(255) NOT NULL UNIQUE,
    "phone_number" varchar(20) NOT NULL,
    "last_seen" timestamp,
    "created_on" timestamp DEFAULT now() NOT NULL,
    "modified_on" timestamp DEFAULT now() NOT NULL,
    "created_by" uuid,
    "modified_by" uuid
);

CREATE TABLE IF NOT EXISTS "treedev"."principals" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
    "name" varchar(255) NOT NULL,
    "email_id" varchar(255) NOT NULL UNIQUE,
    "phone_number" varchar(20) NOT NULL,
    "college_id" uuid NOT NULL,
    "last_seen" timestamp,
    "created_on" timestamp DEFAULT now() NOT NULL,
    "modified_on" timestamp DEFAULT now() NOT NULL,
    "created_by" uuid,
    "modified_by" uuid
);

CREATE TABLE IF NOT EXISTS "treedev"."hods" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
    "name" varchar(255) NOT NULL,
    "email_id" varchar(255) NOT NULL UNIQUE,
    "phone_number" varchar(20) NOT NULL,
    "college_id" uuid NOT NULL,
    "department_id" uuid NOT NULL,
    "last_seen" timestamp,
    "created_on" timestamp DEFAULT now() NOT NULL,
    "modified_on" timestamp DEFAULT now() NOT NULL,
    "created_by" uuid,
    "modified_by" uuid
);

CREATE TABLE IF NOT EXISTS "treedev"."staffs" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
    "name" varchar(255) NOT NULL,
    "email_id" varchar(255) NOT NULL UNIQUE,
    "phone_number" varchar(20) NOT NULL,
    "college_id" uuid NOT NULL,
    "department_id" uuid NOT NULL,
    "last_seen" timestamp,
    "created_on" timestamp DEFAULT now() NOT NULL,
    "modified_on" timestamp DEFAULT now() NOT NULL,
    "created_by" uuid,
    "modified_by" uuid
);

CREATE TABLE IF NOT EXISTS "treedev"."students" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
    "name" varchar(255) NOT NULL,
    "email_id" varchar(255) NOT NULL UNIQUE,
    "phone_number" varchar(20) NOT NULL,
    "college_id" uuid NOT NULL,
    "department_id" uuid NOT NULL,
    "class" varchar(50),
    "semester" varchar(20),
    "roll_number" varchar(50),
    "last_seen" timestamp,
    "created_on" timestamp DEFAULT now() NOT NULL,
    "modified_on" timestamp DEFAULT now() NOT NULL,
    "created_by" uuid,
    "modified_by" uuid
);

-- Add foreign key constraints
ALTER TABLE "treedev"."principals" ADD CONSTRAINT "principals_college_id_fkey" 
    FOREIGN KEY ("college_id") REFERENCES "treedev"."colleges"("id") ON DELETE CASCADE;

ALTER TABLE "treedev"."hods" ADD CONSTRAINT "hods_college_id_fkey" 
    FOREIGN KEY ("college_id") REFERENCES "treedev"."colleges"("id") ON DELETE CASCADE;

ALTER TABLE "treedev"."hods" ADD CONSTRAINT "hods_department_id_fkey" 
    FOREIGN KEY ("department_id") REFERENCES "treedev"."departments"("id") ON DELETE CASCADE;

ALTER TABLE "treedev"."staffs" ADD CONSTRAINT "staffs_college_id_fkey" 
    FOREIGN KEY ("college_id") REFERENCES "treedev"."colleges"("id") ON DELETE CASCADE;

ALTER TABLE "treedev"."staffs" ADD CONSTRAINT "staffs_department_id_fkey" 
    FOREIGN KEY ("department_id") REFERENCES "treedev"."departments"("id") ON DELETE CASCADE;

ALTER TABLE "treedev"."students" ADD CONSTRAINT "students_college_id_fkey" 
    FOREIGN KEY ("college_id") REFERENCES "treedev"."colleges"("id") ON DELETE CASCADE;

ALTER TABLE "treedev"."students" ADD CONSTRAINT "students_department_id_fkey" 
    FOREIGN KEY ("department_id") REFERENCES "treedev"."departments"("id") ON DELETE CASCADE;

-- Migrate existing data from users table to role-specific tables
-- Admin users
INSERT INTO "treedev"."admin_users" (id, name, email_id, phone_number, last_seen, created_on, modified_on)
SELECT id, name, email, phone, last_login, created_at, updated_at
FROM "treedev"."users" 
WHERE role = 'admin';

-- Principal users
INSERT INTO "treedev"."principals" (id, name, email_id, phone_number, college_id, last_seen, created_on, modified_on)
SELECT id, name, email, phone, college_id, last_login, created_at, updated_at
FROM "treedev"."users" 
WHERE role = 'principal' AND college_id IS NOT NULL;

-- HOD users
INSERT INTO "treedev"."hods" (id, name, email_id, phone_number, college_id, department_id, last_seen, created_on, modified_on)
SELECT id, name, email, phone, college_id, department_id, last_login, created_at, updated_at
FROM "treedev"."users" 
WHERE role = 'hod' AND college_id IS NOT NULL AND department_id IS NOT NULL;

-- Staff users
INSERT INTO "treedev"."staffs" (id, name, email_id, phone_number, college_id, department_id, last_seen, created_on, modified_on)
SELECT id, name, email, phone, college_id, department_id, last_login, created_at, updated_at
FROM "treedev"."users" 
WHERE role = 'staff' AND college_id IS NOT NULL AND department_id IS NOT NULL;

-- Student users
INSERT INTO "treedev"."students" (id, name, email_id, phone_number, college_id, department_id, class, semester, roll_number, last_seen, created_on, modified_on)
SELECT id, name, email, phone, college_id, department_id, class, semester, roll_number, last_login, created_at, updated_at
FROM "treedev"."users" 
WHERE role = 'student' AND college_id IS NOT NULL AND department_id IS NOT NULL;

-- Create backup of original users table before modifying
CREATE TABLE IF NOT EXISTS "treedev"."users_backup" AS SELECT * FROM "treedev"."users";

-- Remove columns from users table that are now in role-specific tables
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS name;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS phone;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS college_id;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS department_id;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS class_in_charge;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS class;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS semester;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS roll_number;
ALTER TABLE "treedev"."users" DROP COLUMN IF EXISTS last_login;

-- Add last_seen column to users table
ALTER TABLE "treedev"."users" ADD COLUMN IF NOT EXISTS last_seen timestamp;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_admin_users_email" ON "treedev"."admin_users"("email_id");
CREATE INDEX IF NOT EXISTS "idx_principals_email" ON "treedev"."principals"("email_id");
CREATE INDEX IF NOT EXISTS "idx_principals_college" ON "treedev"."principals"("college_id");
CREATE INDEX IF NOT EXISTS "idx_hods_email" ON "treedev"."hods"("email_id");
CREATE INDEX IF NOT EXISTS "idx_hods_college" ON "treedev"."hods"("college_id");
CREATE INDEX IF NOT EXISTS "idx_hods_department" ON "treedev"."hods"("department_id");
CREATE INDEX IF NOT EXISTS "idx_staffs_email" ON "treedev"."staffs"("email_id");
CREATE INDEX IF NOT EXISTS "idx_staffs_college" ON "treedev"."staffs"("college_id");
CREATE INDEX IF NOT EXISTS "idx_staffs_department" ON "treedev"."staffs"("department_id");
CREATE INDEX IF NOT EXISTS "idx_students_email" ON "treedev"."students"("email_id");
CREATE INDEX IF NOT EXISTS "idx_students_college" ON "treedev"."students"("college_id");
CREATE INDEX IF NOT EXISTS "idx_students_department" ON "treedev"."students"("department_id");
CREATE INDEX IF NOT EXISTS "idx_students_roll_number" ON "treedev"."students"("roll_number");
