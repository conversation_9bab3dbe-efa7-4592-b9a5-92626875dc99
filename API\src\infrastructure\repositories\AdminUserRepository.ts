import { eq } from 'drizzle-orm';
import { db } from '../database/connection';
import { adminUsers } from '../database/schema';
import { AdminUser, CreateAdminUserData, UpdateAdminUserData } from '../../domain/entities/AdminUser';
import { IAdminUserRepository } from '../../domain/repositories/IAdminUserRepository';

export class AdminUserRepository implements IAdminUserRepository {
  async create(data: CreateAdminUserData): Promise<AdminUser> {
    const [adminUser] = await db.insert(adminUsers).values({
      name: data.name,
      emailId: data.emailId,
      phone: data.phone,
      createdBy: data.createdBy || null,
    }).returning();

    return this.mapToEntity(adminUser);
  }

  async findById(id: string): Promise<AdminUser | null> {
    const [adminUser] = await db.select().from(adminUsers).where(eq(adminUsers.id, id));
    return adminUser ? this.mapToEntity(adminUser) : null;
  }

  async findByEmail(email: string): Promise<AdminUser | null> {
    const [adminUser] = await db.select().from(adminUsers).where(eq(adminUsers.emailId, email));
    return adminUser ? this.mapToEntity(adminUser) : null;
  }

  async findAll(): Promise<AdminUser[]> {
    const results = await db.select().from(adminUsers);
    return results.map(this.mapToEntity);
  }

  async update(id: string, data: UpdateAdminUserData): Promise<AdminUser | null> {
    const updateData: any = {
      modifiedOn: new Date(),
    };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.lastSeen !== undefined) updateData.lastSeen = data.lastSeen;
    if (data.modifiedBy !== undefined) updateData.modifiedBy = data.modifiedBy;

    const [updated] = await db.update(adminUsers)
      .set(updateData)
      .where(eq(adminUsers.id, id))
      .returning();

    return updated ? this.mapToEntity(updated) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(adminUsers).where(eq(adminUsers.id, id));
    return result.rowCount > 0;
  }

  private mapToEntity(row: any): AdminUser {
    return {
      id: row.id,
      name: row.name,
      emailId: row.emailId,
      phone: row.phone,
      lastSeen: row.lastSeen,
      createdOn: row.createdOn,
      modifiedOn: row.modifiedOn,
      createdBy: row.createdBy,
      modifiedBy: row.modifiedBy,
    };
  }
}
