import { Staff, CreateStaffData, UpdateStaffData } from '../entities/Staff';

export interface IStaffRepository {
  create(data: CreateStaffData): Promise<Staff>;
  findById(id: string): Promise<Staff | null>;
  findByEmail(email: string): Promise<Staff | null>;
  findByCollegeId(collegeId: string): Promise<Staff[]>;
  findByDepartmentId(departmentId: string): Promise<Staff[]>;
  findAll(): Promise<Staff[]>;
  update(id: string, data: UpdateStaffData): Promise<Staff | null>;
  delete(id: string): Promise<boolean>;
}
