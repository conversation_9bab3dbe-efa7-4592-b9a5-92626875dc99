import React, { useState } from 'react';
import { Plus, Edit, Trash2, Search, Filter, FileText, AlertCircle } from 'lucide-react';
import { Department, User as UserType, College } from '../../types';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../UI/Toast';
import ConfirmDialog from '../UI/ConfirmDialog';
import {
  useDepartments,
  useCreateDepartment,
  useUpdateDepartment,
  useDeleteDepartment
} from '../../hooks/useDepartmentQueries';
import { useColleges } from '../../hooks/useCollegeQueries';

const DepartmentManagement: React.FC = () => {
  const { user } = useAuth();
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [collegeFilter, setCollegeFilter] = useState<string>('all');

  // Toast and confirmation dialog states
  const toast = useToast();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [departmentToDelete, setDepartmentToDelete] = useState<Department | null>(null);

  // TanStack Query hooks
  const { data: departments = [], isLoading, error } = useDepartments();
  const { data: colleges = [] } = useColleges();
  const createDepartmentMutation = useCreateDepartment();
  const updateDepartmentMutation = useUpdateDepartment();
  const deleteDepartmentMutation = useDeleteDepartment();

  // Handle query error with toast
  if (error) {
    toast.error('Loading Error', error.message || 'Failed to load departments');
  }

  const getCollegeName = (collegeId: string) => {
    const college = colleges.find(c => c.id === collegeId);
    return college?.name || 'Unknown College';
  };

  const handleAddDepartment = () => {
    setSelectedDepartment(null);
    setShowAddForm(true);
  };

  const handleEditDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setShowAddForm(true);
  };

  const handleDeleteDepartment = (department: Department) => {
    setDepartmentToDelete(department);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteDepartment = async () => {
    if (!departmentToDelete) return;

    deleteDepartmentMutation.mutate(departmentToDelete.id, {
      onSuccess: () => {
        toast.success('Department Deleted', `${departmentToDelete.name} has been successfully deleted.`);
        setShowDeleteConfirm(false);
        setDepartmentToDelete(null);
      },
      onError: (err: any) => {
        console.error('Error deleting department:', err);
        const errorMessage = err.response?.data?.message || err.message || 'Failed to delete department';
        toast.error('Delete Failed', errorMessage);
      }
    });
  };

  const cancelDeleteDepartment = () => {
    setShowDeleteConfirm(false);
    setDepartmentToDelete(null);
  };

  // CSV Export functionality
  const exportToCSV = () => {
    const csvHeaders = [
      'Department Name',
      'Code',
      'College',
      'Total Students',
      'Established',
      'Last Updated'
    ];

    const csvData = filteredDepartments.map(department => [
      department.name,
      department.code,
      getCollegeName(department.collegeId),
      // Mock student data - replace with actual data when available
      department.id === '1' ? '300' : department.id === '2' ? '250' : department.id === '3' ? '100' : department.id === '4' ? '700' : '240',
      department.established,
      new Date().toLocaleString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      })
    ]);

    // Create CSV content
    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row =>
        row.map(field =>
          // Escape fields that contain commas, quotes, or newlines
          typeof field === 'string' && (field.includes(',') || field.includes('"') || field.includes('\n'))
            ? `"${field.replace(/"/g, '""')}"`
            : field
        ).join(',')
      )
    ].join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `departments_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success toast
    toast.success('Export Successful', `${filteredDepartments.length} departments exported to CSV file.`);
  };

  // Filter departments based on search and college
  const filteredDepartments = departments.filter(department => {
    const matchesSearch = department.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         department.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCollege = collegeFilter === 'all' || department.collegeId === collegeFilter;
    return matchesSearch && matchesCollege;
  });

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Departments</h1>
          <p className="text-gray-600">Manage departments for participating colleges</p>
        </div>
        <button
          onClick={handleAddDepartment}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Department</span>
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by Department Name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={collegeFilter}
              onChange={(e) => setCollegeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">Filter by College</option>
              {colleges.map(college => (
                <option key={college.id} value={college.id}>{college.name}</option>
              ))}
            </select>
            <button
              onClick={exportToCSV}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
              title="Export to CSV"
            >
              <FileText className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department Name ↓
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  College ↓
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Students ↓
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status ↓
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Updated ↓
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredDepartments.map((department, index) => (
                <tr key={department.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{department.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{getCollegeName(department.collegeId)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {/* Mock data for total students - you can replace with actual data */}
                      {department.id === '1' ? '300' : department.id === '2' ? '250' : department.id === '3' ? '100' : department.id === '4' ? '700' : '240'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date().toLocaleString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true,
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric'
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditDepartment(department)}
                        className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded"
                        title="Edit"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteDepartment(department)}
                        className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredDepartments.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No departments found matching your criteria.</div>
          </div>
        )}
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <DepartmentForm
          department={selectedDepartment}
          colleges={colleges}
          onClose={() => setShowAddForm(false)}
          onSave={async (department) => {
            if (selectedDepartment) {
              // Update existing department
              const { id, ...updateData } = department;
              updateDepartmentMutation.mutate(
                { id: id, data: updateData },
                {
                  onSuccess: (updatedDepartment) => {
                    toast.success('Department Updated', `${updatedDepartment.name} has been successfully updated.`);
                    setShowAddForm(false);
                    setSelectedDepartment(null);
                  },
                  onError: (err: any) => {
                    console.error('Error updating department:', err);
                    const errorMessage = err.response?.data?.message || err.message || 'Failed to update department';
                    toast.error('Update Failed', errorMessage);
                  }
                }
              );
            } else {
              // Create new department
              const createData = {
                name: department.name,
                code: department.code,
                collegeId: department.collegeId,
                established: department.established
                // hodId is optional and will be handled by the backend
                // totalStudents and totalStaff are calculated fields, not input fields
              };

              createDepartmentMutation.mutate(createData, {
                onSuccess: (newDepartment) => {
                  toast.success('Department Added', `${newDepartment.name} has been successfully added.`);
                  setShowAddForm(false);
                  setSelectedDepartment(null);
                },
                onError: (err: any) => {
                  console.error('Error creating department:', err);
                  const errorMessage = err.response?.data?.message || err.message || 'Failed to create department';
                  toast.error('Create Failed', errorMessage);
                }
              });
            }
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={cancelDeleteDepartment}
        onConfirm={confirmDeleteDepartment}
        title="Delete Department"
        message={`Are you sure you want to delete "${departmentToDelete?.name}"? This action cannot be undone and will remove all associated data.`}
        confirmText="Delete Department"
        cancelText="Cancel"
        type="danger"
        loading={deleteDepartmentMutation.isPending}
      />
    </div>
  );
};

// Department Form Component
interface DepartmentFormProps {
  department: Department | null;
  colleges: College[];
  onClose: () => void;
  onSave: (department: any) => void;
}

const DepartmentForm: React.FC<DepartmentFormProps> = ({ department, colleges, onClose, onSave }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: department?.name || '',
    code: department?.code || '',
    collegeId: department?.collegeId || user?.collegeId || '',
    established: department?.established || new Date().getFullYear().toString()
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const departmentData: any = {
      name: formData.name,
      code: formData.code,
      collegeId: formData.collegeId,
      established: formData.established
    };

    // Only include id for updates, not for creation
    if (department?.id) {
      departmentData.id = department.id;
    }

    onSave(departmentData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">
          {department ? 'Edit Department' : 'Add New Department'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Department Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="e.g., Computer Science & Engineering"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Department Code</label>
            <input
              type="text"
              value={formData.code}
              onChange={(e) => setFormData({...formData, code: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="e.g., CSE"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">College</label>
            <select
              value={formData.collegeId}
              onChange={(e) => setFormData({...formData, collegeId: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            >
              <option value="">Select College</option>
              {colleges.map(college => (
                <option key={college.id} value={college.id}>{college.name}</option>
              ))}
            </select>
          </div>



          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Established Year</label>
            <input
              type="text"
              value={formData.established}
              onChange={(e) => setFormData({...formData, established: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="e.g., 1985"
              required
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
            >
              {department ? 'Update' : 'Create'} Department
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DepartmentManagement;