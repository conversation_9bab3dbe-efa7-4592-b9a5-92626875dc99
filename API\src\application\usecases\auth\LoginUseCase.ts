import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IAuthService, LoginCredentials, AuthResult } from '../../../domain/services/IAuthService';
import { UnauthorizedError } from '../../../presentation/middleware/errorHandler';

export class LoginUseCase {
  constructor(
    private userRepository: IUserRepository,
    private authService: IAuthService
  ) {}

  async execute(credentials: LoginCredentials): Promise<AuthResult> {
    const { email, password } = credentials;

    // Find user by email
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      throw new UnauthorizedError('Invalid email or password');
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new UnauthorizedError('Account is not active');
    }

    // Verify password
    if (!user.password) {
      throw new UnauthorizedError('Account not properly set up');
    }

    const isPasswordValid = await this.authService.comparePassword(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedError('Invalid email or password');
    }

    // Update last login
    await this.userRepository.update(user.id, { lastLogin: new Date() });

    // Generate token
    const token = this.authService.generateToken(user);

    return {
      user: {
        ...user,
        password: undefined, // Don't return password
      },
      token,
    };
  }
}
