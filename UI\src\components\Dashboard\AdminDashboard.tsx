import React from 'react';
import {
  Users,
  TreePine,
  Building,
  GraduationCap,
  CheckCircle,
  Clock,
  XCircle,
  TrendingUp,
  Calendar,
  Award
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import { useTreePlantingStats } from '../../hooks/useTreePlantings';
import { useColleges } from '../../hooks/useColleges';
import { useUserStats } from '../../hooks/useUserQueries';

const AdminDashboard: React.FC = () => {
  const { data: userStats, loading: userStatsLoading } = useUserStats();
  const { data: treePlantingStats, loading: treePlantingStatsLoading } = useTreePlantingStats();
  const { data: colleges, loading: collegesLoading } = useColleges();

  const loading = userStatsLoading || treePlantingStatsLoading || collegesLoading;

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const currentMonth = new Date().toLocaleString('default', { month: 'long' });

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">Overview of the Tree Planting Initiative</p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Calendar className="h-4 w-4" />
          <span>Last updated: {new Date().toLocaleDateString()}</span>
        </div>
      </div>

      {/* Main Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatisticsCard
          title="Total Trees Planted"
          value={treePlantingStats?.totalTrees || 0}
          icon={TreePine}
          color="green"
          subtitle="All time"
        />
        <StatisticsCard
          title="Total Students"
          value={userStats?.totalStudents || 0}
          icon={GraduationCap}
          color="blue"
          subtitle="Active students"
        />
        <StatisticsCard
          title="Total Colleges"
          value={colleges?.length || 0}
          icon={Building}
          color="purple"
          subtitle="Registered colleges"
        />
        <StatisticsCard
          title="Total Staff"
          value={(userStats?.totalStaff || 0) + (userStats?.totalHODs || 0) + (userStats?.totalPrincipals || 0)}
          icon={Users}
          color="indigo"
          subtitle="All staff members"
        />
      </div>

      {/* Tree Planting Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatisticsCard
          title="Pending Verification"
          value={treePlantingStats?.pendingVerification || 0}
          icon={Clock}
          color="yellow"
          subtitle="Awaiting review"
        />
        <StatisticsCard
          title="Approved Plantings"
          value={treePlantingStats?.approved || 0}
          icon={CheckCircle}
          color="green"
          subtitle="Verified trees"
        />
        <StatisticsCard
          title="Rejected Submissions"
          value={treePlantingStats?.rejected || 0}
          icon={XCircle}
          color="red"
          subtitle="Need resubmission"
        />
      </div>

      {/* Monthly Progress */}
      {treePlantingStats?.byMonth && treePlantingStats.byMonth.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Monthly Progress</h2>
            <TrendingUp className="h-5 w-5 text-green-600" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {treePlantingStats.byMonth.slice(-6).map((month, index) => (
              <div key={index} className="text-center">
                <div className="bg-green-50 rounded-lg p-4 mb-2">
                  <div className="text-2xl font-bold text-green-600">{month.count}</div>
                </div>
                <div className="text-sm text-gray-600">{month.month}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Department-wise Statistics */}
      {treePlantingStats?.byDepartment && treePlantingStats.byDepartment.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Top Performing Departments</h2>
            <Award className="h-5 w-5 text-yellow-600" />
          </div>
          <div className="space-y-4">
            {treePlantingStats.byDepartment.slice(0, 5).map((dept, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                  }`}>
                    {index + 1}
                  </div>
                  <span className="font-medium text-gray-900">{dept.departmentName}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <TreePine className="h-4 w-4 text-green-600" />
                  <span className="font-bold text-green-600">{dept.count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Activity Summary */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">System Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">User Distribution</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Students</span>
                <span className="font-semibold">{userStats?.totalStudents || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Staff</span>
                <span className="font-semibold">{userStats?.totalStaff || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">HODs</span>
                <span className="font-semibold">{userStats?.totalHODs || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Principals</span>
                <span className="font-semibold">{userStats?.totalPrincipals || 0}</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Verification Status</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Approval Rate</span>
                <span className="font-semibold text-green-600">
                  {treePlantingStats?.totalTrees ?
                    Math.round((treePlantingStats.approved / treePlantingStats.totalTrees) * 100) : 0}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Pending Review</span>
                <span className="font-semibold text-yellow-600">{treePlantingStats?.pendingVerification || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Rejected</span>
                <span className="font-semibold text-red-600">{treePlantingStats?.rejected || 0}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;