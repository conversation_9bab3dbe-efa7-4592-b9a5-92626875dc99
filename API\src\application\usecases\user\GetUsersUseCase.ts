import { IUserRepository, UserFilters } from '../../../domain/repositories/IUserRepository';
import { User, UserRole } from '../../../domain/entities/User';
import { ForbiddenError } from '../../../presentation/middleware/errorHandler';

export interface GetUsersRequest {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  filters?: UserFilters;
}

export class GetUsersUseCase {
  constructor(private userRepository: IUserRepository) {}

  async execute(request: GetUsersRequest): Promise<User[]> {
    const { requesterId, requesterRole, requesterCollegeId, requesterDepartmentId, filters = {} } = request;

    // Apply role-based filtering
    const filteredFilters = this.applyRoleBasedFilters(
      requesterRole,
      requesterCollegeId,
      requesterDepartmentId,
      filters
    );

    const users = await this.userRepository.findAll(filteredFilters);

    // Remove passwords from response
    return users.map(user => ({
      ...user,
      password: undefined,
    }));
  }

  private applyRoleBasedFilters(
    requesterRole: UserRole,
    requesterCollegeId?: string,
    requesterDepartmentId?: string,
    filters: UserFilters = {}
  ): UserFilters {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can see all users
        return filters;

      case UserRole.PRINCIPAL:
        // Principal can see users in their college
        if (!requesterCollegeId) {
          throw new ForbiddenError('Principal must be assigned to a college');
        }
        return {
          ...filters,
          collegeId: requesterCollegeId,
        };

      case UserRole.HOD:
        // HOD can see users in their department
        if (!requesterDepartmentId) {
          throw new ForbiddenError('HOD must be assigned to a department');
        }
        return {
          ...filters,
          departmentId: requesterDepartmentId,
        };

      case UserRole.STAFF:
        // Staff can see students in their department
        if (!requesterDepartmentId) {
          throw new ForbiddenError('Staff must be assigned to a department');
        }
        return {
          ...filters,
          departmentId: requesterDepartmentId,
          role: UserRole.STUDENT,
        };

      case UserRole.STUDENT:
        // Students can only see themselves
        throw new ForbiddenError('Students cannot access user lists');

      default:
        throw new ForbiddenError('Invalid role');
    }
  }
}
