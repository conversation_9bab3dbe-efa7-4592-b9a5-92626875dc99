import { IInvitationRepository } from '../../../domain/repositories/IInvitationRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { IEmailService } from '../../../domain/services/IEmailService';
import { CreateInvitationData, Invitation } from '../../../domain/entities/Invitation';
import { UserRole } from '../../../domain/entities/User';
import { ValidationError, ForbiddenError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface SendInvitationRequest {
  email: string;
  role: UserRole;
  collegeId: string;
  departmentId?: string;
  sentBy: string;
}

export class SendInvitationUseCase {
  constructor(
    private invitationRepository: IInvitationRepository,
    private userRepository: IUserRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository,
    private emailService: IEmailService
  ) {}

  async execute(request: SendInvitationRequest): Promise<Invitation> {
    const { email, role, collegeId, departmentId, sentBy } = request;

    // Validate sender permissions
    await this.validateSenderPermissions(sentBy, role, collegeId, departmentId);

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Check for existing pending invitations
    const existingInvitations = await this.invitationRepository.findByEmail(email);
    const pendingInvitation = existingInvitations.find(inv => inv.status === 'pending');
    if (pendingInvitation) {
      throw new ConflictError('Pending invitation already exists for this email');
    }

    // Validate college and department
    const college = await this.collegeRepository.findById(collegeId);
    if (!college) {
      throw new ValidationError('College not found');
    }

    let department = null;
    if (departmentId) {
      department = await this.departmentRepository.findById(departmentId);
      if (!department || department.collegeId !== collegeId) {
        throw new ValidationError('Department not found or does not belong to the specified college');
      }
    }

    // Create invitation
    const invitationData: CreateInvitationData = {
      email,
      role,
      sentBy,
      collegeId,
      departmentId,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    };

    const invitation = await this.invitationRepository.create(invitationData);

    // Get sender information
    const sender = await this.userRepository.findById(sentBy);
    if (!sender) {
      throw new ValidationError('Sender not found');
    }

    // Send invitation email
    const invitationLink = `${process.env.FRONTEND_URL}/register?token=${invitation.token}`;
    
    const emailSent = await this.emailService.sendInvitation(email, {
      recipientName: email.split('@')[0], // Use email prefix as name
      senderName: sender.name,
      role,
      collegeName: college.name,
      departmentName: department?.name,
      invitationLink,
      expiresAt: invitation.expiresAt,
    });

    if (!emailSent) {
      // If email fails, delete the invitation
      await this.invitationRepository.delete(invitation.id);
      throw new Error('Failed to send invitation email');
    }

    return invitation;
  }

  private async validateSenderPermissions(
    senderId: string,
    targetRole: UserRole,
    collegeId: string,
    departmentId?: string
  ): Promise<void> {
    const sender = await this.userRepository.findById(senderId);
    if (!sender) {
      throw new ValidationError('Sender not found');
    }

    const senderRole = sender.role;

    // Define role hierarchy and permissions
    const roleHierarchy: Record<UserRole, UserRole[]> = {
      [UserRole.ADMIN]: [UserRole.PRINCIPAL],
      [UserRole.PRINCIPAL]: [UserRole.HOD, UserRole.STAFF], // Principal can invite both HOD and Staff
      [UserRole.HOD]: [UserRole.STAFF, UserRole.STUDENT],
      [UserRole.STAFF]: [UserRole.STUDENT],
      [UserRole.STUDENT]: [], // Students cannot invite anyone
    };

    // Check if sender can invite the target role
    const allowedRoles = roleHierarchy[senderRole];
    if (!allowedRoles || !allowedRoles.includes(targetRole)) {
      throw new ForbiddenError(`${senderRole} cannot invite ${targetRole}`);
    }

    // Additional validation based on sender role
    switch (senderRole) {
      case UserRole.ADMIN:
        // Admin can invite principals to any college
        break;

      case UserRole.PRINCIPAL:
        // Principal can only invite to their own college
        if (sender.collegeId !== collegeId) {
          throw new ForbiddenError('Principal can only invite to their own college');
        }
        // For Staff invitations, department is required
        if (targetRole === UserRole.STAFF && !departmentId) {
          throw new ValidationError('Department ID is required when inviting Staff');
        }
        break;

      case UserRole.HOD:
        // HOD can only invite to their own department
        if (sender.collegeId !== collegeId || sender.departmentId !== departmentId) {
          throw new ForbiddenError('HOD can only invite to their own department');
        }
        if (!departmentId) {
          throw new ValidationError('Department ID is required when HOD sends invitation');
        }
        break;

      case UserRole.STAFF:
        // Staff can only invite students to their own department
        if (sender.collegeId !== collegeId || sender.departmentId !== departmentId) {
          throw new ForbiddenError('Staff can only invite students to their own department');
        }
        if (!departmentId) {
          throw new ValidationError('Department ID is required when staff sends invitation');
        }
        if (targetRole !== UserRole.STUDENT) {
          throw new ForbiddenError('Staff can only invite students');
        }
        break;

      default:
        throw new ForbiddenError('Invalid sender role for sending invitations');
    }
  }
}
