export interface AdminUser {
  id: string;
  name: string;
  emailId: string;
  phone: string;
  lastSeen?: Date;
  createdOn: Date;
  modifiedOn: Date;
  createdBy?: string;
  modifiedBy?: string;
}

export interface CreateAdminUserData {
  name: string;
  emailId: string;
  phone: string;
  createdBy?: string;
}

export interface UpdateAdminUserData {
  name?: string;
  phone?: string;
  lastSeen?: Date;
  modifiedBy?: string;
}
