export interface Principal {
  id: string;
  name: string;
  emailId: string;
  phone: string;
  collegeId: string;
  lastSeen?: Date;
  createdOn: Date;
  modifiedOn: Date;
  createdBy?: string;
  modifiedBy?: string;
}

export interface CreatePrincipalData {
  name: string;
  emailId: string;
  phone: string;
  collegeId: string;
  createdBy?: string;
}

export interface UpdatePrincipalData {
  name?: string;
  phone?: string;
  collegeId?: string;
  lastSeen?: Date;
  modifiedBy?: string;
}
