const postgres = require('postgres');
require('dotenv').config();

async function checkDatabaseSchema() {
  const sql = postgres({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
  });

  try {
    console.log('✅ Connected to database');

    // Check if treedev schema exists
    const schemaResult = await sql`
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name = 'treedev'
    `;
    console.log('📁 Treedev schema exists:', schemaResult.length > 0);

    // Check colleges table structure
    const tableResult = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_schema = 'treedev' AND table_name = 'colleges'
      ORDER BY ordinal_position
    `;
    
    console.log('🏫 Colleges table structure:');
    if (tableResult.length === 0) {
      console.log('❌ Colleges table does not exist in treedev schema');

      // Check if it exists in public schema
      const publicTableResult = await sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'colleges'
        ORDER BY ordinal_position
      `;

      if (publicTableResult.length > 0) {
        console.log('🏫 Colleges table found in public schema:');
        publicTableResult.forEach(row => {
          console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
        });
      }
    } else {
      tableResult.forEach(row => {
        console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
      });

      // Check if code column exists
      const hasCodeColumn = tableResult.some(row => row.column_name === 'code');
      console.log('🔍 Code column exists:', hasCodeColumn);
    }

    // Check enums
    const enumResult = await sql`
      SELECT t.typname, e.enumlabel
      FROM pg_type t
      JOIN pg_enum e ON t.oid = e.enumtypid
      JOIN pg_namespace n ON n.oid = t.typnamespace
      WHERE n.nspname = 'treedev'
      ORDER BY t.typname, e.enumsortorder
    `;

    console.log('📋 Enums in treedev schema:');
    const enums = {};
    enumResult.forEach(row => {
      if (!enums[row.typname]) enums[row.typname] = [];
      enums[row.typname].push(row.enumlabel);
    });

    Object.keys(enums).forEach(enumName => {
      console.log(`  - ${enumName}: [${enums[enumName].join(', ')}]`);
    });

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await sql.end();
  }
}

checkDatabaseSchema();
