import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthContext, useAuthProvider } from './hooks/useAuth';
import Login from './components/Auth/Login';
import SetPassword from './components/Auth/SetPassword';
import DashboardLayout from './components/Layout/DashboardLayout';
import { ToastProvider } from './components/UI/Toast';
import { queryClient } from './lib/queryClient';

// Component to redirect /register to /set-password with query params
function RegisterRedirect() {
  const location = useLocation();
  return <Navigate to={`/set-password${location.search}`} replace />;
}

function App() {
  const auth = useAuthProvider();

  if (auth.loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ToastProvider>
        <AuthContext.Provider value={auth}>
          <Router>
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={!auth.user ? <Login /> : <Navigate to="/dashboard" replace />} />
              <Route path="/set-password" element={!auth.user ? <SetPassword /> : <Navigate to="/dashboard" replace />} />

              {/* Legacy route - redirect old register links to set-password */}
              <Route path="/register" element={<RegisterRedirect />} />

              {/* Protected routes */}
              <Route path="/dashboard" element={auth.user ? <DashboardLayout /> : <Navigate to="/login" replace />} />

              {/* Default redirect */}
              <Route path="/" element={<Navigate to={auth.user ? "/dashboard" : "/login"} replace />} />
            </Routes>
          </Router>
        </AuthContext.Provider>
      </ToastProvider>
    </QueryClientProvider>
  );
}

export default App;