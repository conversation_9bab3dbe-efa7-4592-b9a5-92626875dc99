import { Router } from 'express';
import authRoutes from './auth';
import invitationRoutes from './invitations';
import userRoutes from './users';
import treePlantingRoutes from './treePlantings';
import collegeRoutes from './colleges';
import departmentRoutes from './departments';
import courseRoutes from './courses';

const router = Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// API routes
router.use('/auth', authRoutes);
router.use('/invitations', invitationRoutes);
router.use('/users', userRoutes);
router.use('/tree-plantings', treePlantingRoutes);
router.use('/colleges', collegeRoutes);
router.use('/departments', departmentRoutes);
router.use('/courses', courseRoutes);

export default router;
