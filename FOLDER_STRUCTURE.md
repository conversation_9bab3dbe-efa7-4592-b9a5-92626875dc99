# 📁 Tree Planting Initiative - Folder Structure

This document provides a comprehensive overview of the project's folder structure for both API (Backend) and UI (Frontend) components.

## 🏗️ Overall Project Structure

```
tree-planting-initiative/
├── 📁 API/                           # Backend Node.js application
├── 📁 UI/                            # Frontend React application
├── 📄 PROJECT_DOCUMENTATION.md       # Complete project documentation
├── 📄 README.md                      # Main project README
├── 📄 FOLDER_STRUCTURE.md           # This file
├── 📄 test-college-api.js           # API testing script
└── 📄 test-college-update.js        # College update testing script
```

## 🔧 API (Backend) Structure

```
API/
├── 📁 src/                          # Source code directory
│   ├── 📁 application/              # Application layer (Use Cases)
│   │   └── 📁 usecases/
│   │       ├── 📁 auth/             # Authentication use cases
│   │       │   ├── 📄 LoginUseCase.ts
│   │       │   ├── 📄 RegisterUseCase.ts
│   │       │   └── 📄 LogoutUseCase.ts
│   │       ├── 📁 invitation/       # Invitation management
│   │       │   ├── 📄 SendInvitationUseCase.ts
│   │       │   ├── 📄 AcceptInvitationUseCase.ts
│   │       │   └── 📄 VerifyOTPUseCase.ts
│   │       ├── 📁 treePlanting/     # Tree planting operations
│   │       │   ├── 📄 CreateTreePlantingUseCase.ts
│   │       │   ├── 📄 VerifyTreePlantingUseCase.ts
│   │       │   └── 📄 GetTreePlantingStatsUseCase.ts
│   │       └── 📁 user/             # User management
│   │           ├── 📄 CreateUserUseCase.ts
│   │           ├── 📄 UpdateUserUseCase.ts
│   │           └── 📄 GetUserUseCase.ts
│   ├── 📁 domain/                   # Domain layer (Business Logic)
│   │   ├── 📁 entities/             # Domain entities
│   │   │   ├── 📄 User.ts           # User entity and enums
│   │   │   ├── 📄 College.ts        # College entity
│   │   │   ├── 📄 Department.ts     # Department entity
│   │   │   ├── 📄 TreePlanting.ts   # Tree planting entity
│   │   │   ├── 📄 Invitation.ts     # Invitation entity
│   │   │   └── 📄 Course.ts         # Course entity
│   │   ├── 📁 repositories/         # Repository interfaces
│   │   │   ├── 📄 IUserRepository.ts
│   │   │   ├── 📄 ICollegeRepository.ts
│   │   │   ├── 📄 IDepartmentRepository.ts
│   │   │   ├── 📄 ITreePlantingRepository.ts
│   │   │   └── 📄 IInvitationRepository.ts
│   │   └── 📁 services/             # Domain services
│   │       ├── 📄 IAuthService.ts   # Authentication service interface
│   │       ├── 📄 IEmailService.ts  # Email service interface
│   │       └── 📄 IFileService.ts   # File service interface
│   ├── 📁 infrastructure/           # Infrastructure layer
│   │   ├── 📁 database/             # Database configuration
│   │   │   ├── 📁 schema/           # Database schema definitions
│   │   │   │   ├── 📄 users.ts      # Users table schema
│   │   │   │   ├── 📄 colleges.ts   # Colleges table schema
│   │   │   │   ├── 📄 departments.ts # Departments table schema
│   │   │   │   ├── 📄 treePlantings.ts # Tree plantings table schema
│   │   │   │   ├── 📄 invitations.ts # Invitations table schema
│   │   │   │   ├── 📄 courses.ts    # Courses table schema
│   │   │   │   ├── 📄 passwordResets.ts # Password resets table schema
│   │   │   │   └── 📄 index.ts      # Schema exports and relationships
│   │   │   ├── 📄 connection.ts     # Database connection setup
│   │   │   └── 📄 migrate.ts        # Database migration runner
│   │   ├── 📁 repositories/         # Repository implementations
│   │   │   ├── 📄 UserRepository.ts
│   │   │   ├── 📄 CollegeRepository.ts
│   │   │   ├── 📄 DepartmentRepository.ts
│   │   │   ├── 📄 TreePlantingRepository.ts
│   │   │   ├── 📄 InvitationRepository.ts
│   │   │   └── 📄 CourseRepository.ts
│   │   └── 📁 services/             # External service implementations
│   │       ├── 📄 AuthService.ts    # JWT and password hashing
│   │       ├── 📄 EmailService.ts   # Email sending service
│   │       └── 📄 FileService.ts    # File upload and management
│   ├── 📁 presentation/             # Presentation layer (API)
│   │   ├── 📁 controllers/          # Request handlers
│   │   │   ├── 📄 AuthController.ts
│   │   │   ├── 📄 UserController.ts
│   │   │   ├── 📄 CollegeController.ts
│   │   │   ├── 📄 DepartmentController.ts
│   │   │   ├── 📄 TreePlantingController.ts
│   │   │   ├── 📄 InvitationController.ts
│   │   │   └── 📄 CourseController.ts
│   │   ├── 📁 middleware/           # Express middleware
│   │   │   ├── 📄 auth.ts           # Authentication & authorization
│   │   │   ├── 📄 validation.ts     # Request validation
│   │   │   ├── 📄 errorHandler.ts   # Error handling
│   │   │   └── 📄 asyncHandler.ts   # Async error wrapper
│   │   └── 📁 routes/               # API route definitions
│   │       ├── 📄 auth.ts           # Authentication routes
│   │       ├── 📄 users.ts          # User management routes
│   │       ├── 📄 colleges.ts       # College management routes
│   │       ├── 📄 departments.ts    # Department management routes
│   │       ├── 📄 treePlantings.ts  # Tree planting routes
│   │       ├── 📄 invitations.ts    # Invitation routes
│   │       ├── 📄 courses.ts        # Course routes
│   │       └── 📄 index.ts          # Route aggregation
│   └── 📄 index.ts                  # Application entry point
├── 📁 uploads/                      # File upload directory
│   ├── 📁 tree-plantings/          # Tree planting media files
│   └── 📁 temp/                     # Temporary upload files
├── 📁 dist/                         # Compiled JavaScript output
├── 📁 node_modules/                 # Dependencies
├── 📄 package.json                  # Dependencies and scripts
├── 📄 package-lock.json             # Dependency lock file
├── 📄 tsconfig.json                 # TypeScript configuration
├── 📄 drizzle.config.ts             # Drizzle ORM configuration
├── 📄 check-db-schema.js            # Database schema checker
├── 📄 create-admin-user.js          # Admin user creation script
├── 📄 test-college-creation.js      # College creation test
└── 📄 test-invitation-flow.js       # Invitation flow test
```

## 🎨 UI (Frontend) Structure

```
UI/
├── 📁 src/                          # Source code directory
│   ├── 📁 components/               # React components
│   │   ├── 📁 Auth/                 # Authentication components
│   │   │   ├── 📄 Login.tsx         # Login form component
│   │   │   ├── 📄 Register.tsx      # Registration form
│   │   │   ├── 📄 SetPassword.tsx   # Password setup form
│   │   │   └── 📄 ForgotPassword.tsx # Password reset form
│   │   ├── 📁 Charts/               # Chart components
│   │   │   ├── 📄 BarChart.tsx      # Reusable bar chart
│   │   │   ├── 📄 LineChart.tsx     # Line chart component
│   │   │   └── 📄 PieChart.tsx      # Pie chart component
│   │   ├── 📁 Dashboard/            # Dashboard components
│   │   │   ├── 📄 AdminDashboard.tsx
│   │   │   ├── 📄 PrincipalDashboard.tsx
│   │   │   ├── 📄 HODDashboard.tsx
│   │   │   ├── 📄 StaffDashboard.tsx
│   │   │   ├── 📄 StudentDashboard.tsx
│   │   │   ├── 📄 StatisticsCard.tsx # Reusable stats card
│   │   │   ├── 📄 PerformanceOverview.tsx
│   │   │   └── 📄 StudentListTable.tsx
│   │   ├── 📁 Layout/               # Layout components
│   │   │   ├── 📄 Sidebar.tsx       # Navigation sidebar
│   │   │   ├── 📄 Header.tsx        # Top header component
│   │   │   ├── 📄 Layout.tsx        # Main layout wrapper
│   │   │   └── 📄 Navigation.tsx    # Navigation menu
│   │   ├── 📁 Management/           # Management interfaces
│   │   │   ├── 📄 UserManagement.tsx
│   │   │   ├── 📄 CollegeManagement.tsx
│   │   │   ├── 📄 DepartmentManagement.tsx
│   │   │   ├── 📄 InvitationManagement.tsx
│   │   │   └── 📄 CourseManagement.tsx
│   │   ├── 📁 Student/              # Student-specific components
│   │   │   ├── 📄 StudentProfile.tsx
│   │   │   ├── 📄 StudentProgress.tsx
│   │   │   └── 📄 StudentSettings.tsx
│   │   ├── 📁 TreePlanting/         # Tree planting components
│   │   │   ├── 📄 TreePlantingForm.tsx
│   │   │   ├── 📄 TreePlantingList.tsx
│   │   │   ├── 📄 TreePlantingCard.tsx
│   │   │   ├── 📄 VerificationPanel.tsx
│   │   │   └── 📄 MediaUpload.tsx
│   │   └── 📁 UI/                   # Reusable UI components
│   │       ├── 📄 Button.tsx        # Button component
│   │       ├── 📄 Input.tsx         # Input field component
│   │       ├── 📄 Modal.tsx         # Modal dialog component
│   │       ├── 📄 LoadingSpinner.tsx
│   │       ├── 📄 ErrorBoundary.tsx
│   │       └── 📄 Toast.tsx         # Notification component
│   ├── 📁 hooks/                    # Custom React hooks
│   │   ├── 📄 useAuth.ts            # Authentication hook
│   │   ├── 📄 useApi.ts             # API calling hook
│   │   ├── 📄 useCollegeQueries.ts  # College data queries
│   │   ├── 📄 useDepartmentQueries.ts
│   │   ├── 📄 useUserQueries.ts     # User data queries
│   │   ├── 📄 useTreePlantings.ts   # Tree planting queries
│   │   ├── 📄 useInvitationQueries.ts
│   │   ├── 📄 useStaffQueries.ts    # Staff-specific queries
│   │   ├── 📄 useClassQueries.ts    # Class data queries
│   │   └── 📄 useCourseQueries.ts   # Course data queries
│   ├── 📁 services/                 # API service layer
│   │   └── 📄 api.ts                # API client and endpoints
│   ├── 📁 types/                    # TypeScript type definitions
│   │   └── 📄 index.ts              # Shared type definitions
│   ├── 📁 lib/                      # Utility libraries
│   │   └── 📄 queryClient.ts        # TanStack Query configuration
│   ├── 📁 assets/                   # Static assets
│   │   ├── 📄 Guidelines.png        # Application guidelines image
│   │   ├── 📄 RMKCollegelogo.gif    # College logo
│   │   ├── 📄 Resources.jpg         # Resources image
│   │   ├── 📄 loginbg.jpg           # Login background
│   │   ├── 📄 loginbgstudent.png    # Student login background
│   │   ├── 📄 mytree.png            # Tree icon
│   │   └── 📄 tree.jpg              # Tree image
│   ├── 📁 data/                     # Mock data (development)
│   │   ├── 📄 colleges.json         # Sample college data
│   │   ├── 📄 departments.json      # Sample department data
│   │   ├── 📄 users.json            # Sample user data
│   │   └── 📄 registrationRequests.json
│   ├── 📄 App.tsx                   # Main App component
│   ├── 📄 main.tsx                  # Application entry point
│   ├── 📄 index.css                 # Global styles
│   └── 📄 vite-env.d.ts             # Vite environment types
├── 📁 public/                       # Public static files
├── 📁 node_modules/                 # Dependencies
├── 📄 package.json                  # Dependencies and scripts
├── 📄 package-lock.json             # Dependency lock file
├── 📄 tsconfig.json                 # TypeScript configuration
├── 📄 tsconfig.app.json             # App-specific TypeScript config
├── 📄 tsconfig.node.json            # Node-specific TypeScript config
├── 📄 vite.config.ts                # Vite build configuration
├── 📄 tailwind.config.js            # Tailwind CSS configuration
├── 📄 postcss.config.js             # PostCSS configuration
├── 📄 eslint.config.js              # ESLint configuration
├── 📄 index.html                    # HTML template
├── 📄 README.md                     # Frontend-specific README
├── 📄 API_DOCUMENTATION.md          # API documentation
├── 📄 DEPLOYMENT.md                 # Deployment guide
├── 📄 DEVELOPMENT.md                # Development setup guide
└── 📄 database-setup.sql            # Database setup script
```

## 🔍 Key Directory Explanations

### Backend (API)

- **`src/application/usecases/`**: Contains business logic use cases following clean architecture
- **`src/domain/`**: Core business entities and interfaces, framework-independent
- **`src/infrastructure/`**: External concerns like database, email, file storage
- **`src/presentation/`**: API layer with controllers, routes, and middleware
- **`uploads/`**: File storage directory for uploaded media

### Frontend (UI)

- **`src/components/`**: Organized by feature/domain for better maintainability
- **`src/hooks/`**: Custom React hooks for data fetching and state management
- **`src/services/`**: API communication layer
- **`src/types/`**: TypeScript type definitions shared across components
- **`src/data/`**: Mock data for development and testing

## 📋 File Naming Conventions

### Backend
- **Controllers**: `[Entity]Controller.ts` (e.g., `UserController.ts`)
- **Use Cases**: `[Action][Entity]UseCase.ts` (e.g., `CreateUserUseCase.ts`)
- **Repositories**: `[Entity]Repository.ts` (e.g., `UserRepository.ts`)
- **Entities**: `[Entity].ts` (e.g., `User.ts`)

### Frontend
- **Components**: PascalCase (e.g., `UserManagement.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`)
- **Types**: camelCase (e.g., `index.ts`)
- **Services**: camelCase (e.g., `api.ts`)

## 🎯 Architecture Benefits

1. **Separation of Concerns**: Clear boundaries between layers
2. **Testability**: Easy to unit test individual components
3. **Maintainability**: Organized structure for easy navigation
4. **Scalability**: Can easily add new features without affecting existing code
5. **Reusability**: Shared components and utilities across the application

This folder structure follows industry best practices and clean architecture principles, making the codebase maintainable and scalable for future development.
