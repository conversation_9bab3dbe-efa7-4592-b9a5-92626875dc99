import { eq, and, like, count, sql } from 'drizzle-orm';
import { db } from '../database/connection';
import { users } from '../database/schema/users';
import { IUserRepository, UserFilters } from '../../domain/repositories/IUserRepository';
import { User, CreateUserData, UpdateUserData, UserRole } from '../../domain/entities/User';

export class UserRepository implements IUserRepository {
  async create(userData: CreateUserData): Promise<User> {
    const [user] = await db.insert(users).values({
      email: userData.email,
      name: userData.name,
      password: userData.password,
      role: userData.role,
      phone: userData.phone,
      collegeId: userData.collegeId || null,
      departmentId: userData.departmentId || null,
      classInCharge: userData.classInCharge,
      class: userData.class,
      semester: userData.semester,
      rollNumber: userData.rollNumber,
    }).returning();

    return this.mapToEntity(user);
  }

  async findById(id: string): Promise<User | null> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user ? this.mapToEntity(user) : null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user ? this.mapToEntity(user) : null;
  }

  async findAll(filters?: UserFilters): Promise<User[]> {
    let query = db.select().from(users);
    const conditions = [];

    if (filters?.role) {
      conditions.push(eq(users.role, filters.role));
    }
    if (filters?.status) {
      conditions.push(eq(users.status, filters.status as any));
    }
    if (filters?.collegeId) {
      conditions.push(eq(users.collegeId, filters.collegeId));
    }
    if (filters?.departmentId) {
      conditions.push(eq(users.departmentId, filters.departmentId));
    }
    if (filters?.search) {
      conditions.push(
        sql`${users.name} ILIKE ${`%${filters.search}%`} OR ${users.email} ILIKE ${`%${filters.search}%`}`
      );
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.offset(filters.offset);
    }

    const result = await query;
    return result.map(user => this.mapToEntity(user));
  }

  async update(id: string, userData: UpdateUserData): Promise<User | null> {
    const [user] = await db.update(users)
      .set({
        ...userData,
        updatedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();

    return user ? this.mapToEntity(user) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(users).where(eq(users.id, id));
    return result.rowCount > 0;
  }

  async findByRole(role: UserRole): Promise<User[]> {
    const result = await db.select().from(users).where(eq(users.role, role));
    return result.map(user => this.mapToEntity(user));
  }

  async findByCollege(collegeId: string): Promise<User[]> {
    const result = await db.select().from(users).where(eq(users.collegeId, collegeId));
    return result.map(user => this.mapToEntity(user));
  }

  async findByDepartment(departmentId: string): Promise<User[]> {
    const result = await db.select().from(users).where(eq(users.departmentId, departmentId));
    return result.map(user => this.mapToEntity(user));
  }

  async findStudentsByStaff(staffId: string): Promise<User[]> {
    // Find students in the same department as the staff member
    const [staff] = await db.select().from(users).where(eq(users.id, staffId));
    if (!staff || !staff.departmentId) return [];

    const result = await db.select().from(users)
      .where(and(
        eq(users.role, 'student'),
        eq(users.departmentId, staff.departmentId)
      ));
    
    return result.map(user => this.mapToEntity(user));
  }

  async countByRole(role: UserRole): Promise<number> {
    const [result] = await db.select({ count: count() }).from(users).where(eq(users.role, role));
    return result.count;
  }

  async countByCollege(collegeId: string): Promise<number> {
    const [result] = await db.select({ count: count() }).from(users).where(eq(users.collegeId, collegeId));
    return result.count;
  }

  async countByDepartment(departmentId: string): Promise<number> {
    const [result] = await db.select({ count: count() }).from(users).where(eq(users.departmentId, departmentId));
    return result.count;
  }

  private mapToEntity(user: any): User {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      password: user.password,
      role: user.role,
      phone: user.phone,
      status: user.status,
      collegeId: user.collegeId,
      departmentId: user.departmentId,
      classInCharge: user.classInCharge,
      class: user.class,
      semester: user.semester,
      rollNumber: user.rollNumber,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLogin: user.lastLogin,
    };
  }
}
