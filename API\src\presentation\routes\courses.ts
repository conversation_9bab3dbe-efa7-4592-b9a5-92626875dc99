import { Router } from 'express';
import { CourseController } from '../controllers/CourseController';
import { authenticate, authorize } from '../middleware/auth';
import { validate, uuidParamSchema, paginationSchema } from '../middleware/validation';
import { UserRole } from '../../domain/entities/User';

const router = Router();
const courseController = new CourseController();

// All routes require authentication
router.use(authenticate);

// Create course
router.post(
  '/',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD]),
  courseController.createCourse
);

// Get courses
router.get(
  '/',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(paginationSchema),
  courseController.getCourses
);

// Get course by ID
router.get(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  courseController.getCourseById
);

// Get course statistics
router.get(
  '/:id/stats',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  courseController.getCourseStats
);

// Update course
router.put(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD]),
  validate(uuidParamSchema),
  courseController.updateCourse
);

// Delete course
router.delete(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD]),
  validate(uuidParamSchema),
  courseController.deleteCourse
);

export default router;
