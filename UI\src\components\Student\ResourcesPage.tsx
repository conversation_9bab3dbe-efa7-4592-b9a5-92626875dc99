import React from 'react';
import { BookOpen, Download, ExternalLink, Video, FileText, Image } from 'lucide-react';
import resourcesbg from '../../assets/Resources.jpg';

const ResourcesPage: React.FC = () => {
  const resources = [
    {
      category: "Educational Materials",
      icon: <BookOpen className="w-6 h-6 text-blue-600" />,
      items: [
        {
          title: "Tree Planting Handbook",
          type: "PDF",
          size: "2.5 MB",
          description: "Comprehensive guide covering all aspects of tree planting",
          icon: <FileText className="w-5 h-5 text-red-500" />
        },
        {
          title: "Native Tree Species Guide",
          type: "PDF",
          size: "1.8 MB",
          description: "Detailed information about local tree species",
          icon: <FileText className="w-5 h-5 text-red-500" />
        },
        {
          title: "Seasonal Planting Calendar",
          type: "PDF",
          size: "0.9 MB",
          description: "Best times to plant different tree species",
          icon: <FileText className="w-5 h-5 text-red-500" />
        }
      ]
    },
    {
      category: "Video Tutorials",
      icon: <Video className="w-6 h-6 text-purple-600" />,
      items: [
        {
          title: "How to Plant a Tree - Step by Step",
          type: "Video",
          duration: "12:30",
          description: "Complete tutorial on proper tree planting techniques",
          icon: <Video className="w-5 h-5 text-purple-500" />
        },
        {
          title: "Tree Care and Maintenance",
          type: "Video",
          duration: "8:45",
          description: "Essential care tips for healthy tree growth",
          icon: <Video className="w-5 h-5 text-purple-500" />
        },
        {
          title: "Identifying Tree Diseases",
          type: "Video",
          duration: "15:20",
          description: "Learn to spot and treat common tree problems",
          icon: <Video className="w-5 h-5 text-purple-500" />
        }
      ]
    },
    {
      category: "Reference Images",
      icon: <Image className="w-6 h-6 text-green-600" />,
      items: [
        {
          title: "Proper Planting Depth Examples",
          type: "Image Gallery",
          count: "15 images",
          description: "Visual examples of correct planting techniques",
          icon: <Image className="w-5 h-5 text-green-500" />
        },
        {
          title: "Tree Growth Stages",
          type: "Image Gallery",
          count: "20 images",
          description: "Visual timeline of tree development",
          icon: <Image className="w-5 h-5 text-green-500" />
        },
        {
          title: "Common Tree Problems",
          type: "Image Gallery",
          count: "12 images",
          description: "Visual guide to identifying tree issues",
          icon: <Image className="w-5 h-5 text-green-500" />
        }
      ]
    }
  ];

  const externalLinks = [
    {
      title: "Forest Department Guidelines",
      url: "#",
      description: "Official government guidelines for tree planting"
    },
    {
      title: "Environmental Impact Calculator",
      url: "#",
      description: "Calculate the environmental benefits of your tree"
    },
    {
      title: "Tree Species Database",
      url: "#",
      description: "Comprehensive database of tree species information"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Resources</h1>
          <p className="text-lg text-gray-600">
            Access educational materials, tutorials, and tools to support your tree planting journey.
          </p>
        </div>

            <img
              src={resourcesbg}
              alt="Plant Care Tips Illustration"
              className="w-60 md:w-72 object-contain"
            />

        {/* Resource Categories */}
        <div className="space-y-8">
          {resources.map((category, categoryIndex) => (
            <div key={categoryIndex} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-3 mb-6">
                {category.icon}
                <h2 className="text-xl font-semibold text-gray-900">{category.category}</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {category.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start space-x-3 mb-3">
                      {item.icon}
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 mb-1">{item.title}</h3>
                        <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {item.type} • {item.size || item.duration || item.count}
                          </span>
                          <button className="flex items-center space-x-1 text-green-600 hover:text-green-700 text-sm font-medium">
                            <Download className="w-4 h-4" />
                            <span>Download</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* External Links */}
        <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <ExternalLink className="w-6 h-6 text-orange-600" />
            <h2 className="text-xl font-semibold text-gray-900">External Resources</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {externalLinks.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="block border border-gray-200 rounded-lg p-4 hover:shadow-md hover:border-green-300 transition-all"
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{link.title}</h3>
                  <ExternalLink className="w-4 h-4 text-gray-400" />
                </div>
                <p className="text-sm text-gray-600">{link.description}</p>
              </a>
            ))}
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
          <div className="flex items-start space-x-3">
            <BookOpen className="w-6 h-6 text-blue-600 mt-1" />
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Need Additional Help?
              </h3>
              <p className="text-blue-800 leading-relaxed mb-4">
                If you need additional resources or have specific questions about tree planting, 
                don't hesitate to reach out to your faculty advisor or the environmental committee.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourcesPage;