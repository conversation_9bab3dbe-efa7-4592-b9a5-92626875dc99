import { IInvitationRepository } from '../../../domain/repositories/IInvitationRepository';
import { IEmailService } from '../../../domain/services/IEmailService';
import { NotFoundError, ValidationError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface SendInvitationOTPRequest {
  token: string;
}

export class SendInvitationOTPUseCase {
  constructor(
    private invitationRepository: IInvitationRepository,
    private emailService: IEmailService
  ) {}

  async execute(request: SendInvitationOTPRequest): Promise<{ message: string }> {
    const { token } = request;

    // Find invitation by token
    const invitation = await this.invitationRepository.findByToken(token);
    if (!invitation) {
      throw new NotFoundError('Invalid invitation token');
    }

    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation is no longer valid');
    }

    if (invitation.expiresAt < new Date()) {
      throw new ValidationError('Invitation has expired');
    }

    // Generate 6-digit OTP
    const otp = this.generateOTP();
    const otpExpiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // Update invitation with OTP
    await this.invitationRepository.update(invitation.id, {
      otp,
      otpExpiresAt,
      otpVerified: false,
    });

    // Send OTP via email
    await this.emailService.sendOTP(invitation.email, {
      otp,
      recipientName: invitation.email.split('@')[0],
      purpose: 'invitation verification',
      expiresInMinutes: 15,
    });

    return {
      message: 'OTP sent successfully to your email address',
    };
  }

  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
