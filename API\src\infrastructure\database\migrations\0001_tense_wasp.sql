-- Create the new enum in treedev schema
CREATE TYPE "treedev"."invitation_status" AS ENUM('pending', 'accepted', 'rejected', 'expired');--> statement-breakpoint

-- Add a temporary column with the new enum type
ALTER TABLE "treedev"."invitations" ADD COLUMN "status_new" "treedev"."invitation_status";--> statement-breakpoint

-- Copy data from old column to new column
UPDATE "treedev"."invitations" SET "status_new" = "status"::text::"treedev"."invitation_status";--> statement-breakpoint

-- Drop the old column
ALTER TABLE "treedev"."invitations" DROP COLUMN "status";--> statement-breakpoint

-- Rename the new column to the original name
ALTER TABLE "treedev"."invitations" RENAME COLUMN "status_new" TO "status";--> statement-breakpoint

-- Set the default value and not null constraint
ALTER TABLE "treedev"."invitations" ALTER COLUMN "status" SET DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE "treedev"."invitations" ALTER COLUMN "status" SET NOT NULL;--> statement-breakpoint

-- Drop the old enum
DROP TYPE "public"."invitation_status";