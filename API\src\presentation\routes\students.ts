import { Router } from 'express';
import { StudentController } from '../controllers/StudentController';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '../../domain/entities/User';
import { z } from 'zod';
import { validate } from '../middleware/validation';

const router = Router();
const studentController = new StudentController();

// Validation schemas
const createStudentProfileSchema = z.object({
  body: z.object({
    name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
    email: z.string().email('Invalid email format'),
    phone: z.string().min(10, 'Phone number must be at least 10 digits').max(20, 'Phone number too long'),
    collegeId: z.string().uuid('Invalid college ID').optional(),
    departmentId: z.string().uuid('Invalid department ID').optional(),
    class: z.string().max(50, 'Class name too long').optional(),
    semester: z.string().max(20, 'Semester too long').optional(),
    rollNumber: z.string().max(50, 'Roll number too long').optional(),
  }),
});

const updateStudentProfileSchema = z.object({
  body: z.object({
    name: z.string().min(1, 'Name is required').max(255, 'Name too long').optional(),
    phone: z.string().min(10, 'Phone number must be at least 10 digits').max(20, 'Phone number too long').optional(),
    class: z.string().max(50, 'Class name too long').optional(),
    semester: z.string().max(20, 'Semester too long').optional(),
    rollNumber: z.string().max(50, 'Roll number too long').optional(),
  }),
});

const getStudentProfilesSchema = z.object({
  query: z.object({
    collegeId: z.string().uuid('Invalid college ID').optional(),
    departmentId: z.string().uuid('Invalid department ID').optional(),
  }),
});

// Routes
router.post(
  '/profiles',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(createStudentProfileSchema),
  studentController.createStudentProfile
);

router.get(
  '/profiles',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(getStudentProfilesSchema),
  studentController.getStudentProfiles
);

router.get(
  '/profiles/:id',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  studentController.getStudentProfile
);

router.get(
  '/profiles/email/:email',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  studentController.getStudentProfileByEmail
);

router.put(
  '/profiles/:id',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(updateStudentProfileSchema),
  studentController.updateStudentProfile
);

router.delete(
  '/profiles/:id',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  studentController.deleteStudentProfile
);

export default router;
