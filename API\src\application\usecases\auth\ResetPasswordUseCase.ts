import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IPasswordResetRepository } from '../../../domain/repositories/IPasswordResetRepository';
import { IAuthService } from '../../../domain/services/IAuthService';
import { NotFoundError, ValidationError } from '../../../presentation/middleware/errorHandler';
import { PasswordResetStatus } from '../../../domain/entities/PasswordReset';

export interface ResetPasswordRequest {
  token: string;
  otp: string;
  newPassword: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export class ResetPasswordUseCase {
  constructor(
    private userRepository: IUserRepository,
    private passwordResetRepository: IPasswordResetRepository,
    private authService: IAuthService
  ) {}

  async execute(request: ResetPasswordRequest): Promise<ResetPasswordResponse> {
    const { token, otp, newPassword } = request;

    // Validate input
    if (!token || !otp || !newPassword) {
      throw new ValidationError('Token, OTP, and new password are required');
    }

    if (newPassword.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long');
    }

    // Find valid password reset record
    const passwordReset = await this.passwordResetRepository.findValidByTokenAndOtp(token, otp);
    if (!passwordReset) {
      throw new NotFoundError('Invalid or expired password reset token/OTP');
    }

    // Check if token is expired
    if (passwordReset.expiresAt < new Date()) {
      await this.passwordResetRepository.update(passwordReset.id, {
        status: PasswordResetStatus.EXPIRED
      });
      throw new ValidationError('Password reset token has expired');
    }

    // Find user
    const user = await this.userRepository.findById(passwordReset.userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Hash new password
    const hashedPassword = await this.authService.hashPassword(newPassword);

    // Update user password
    await this.userRepository.update(user.id, { 
      password: hashedPassword 
    });

    // Mark password reset as used
    await this.passwordResetRepository.update(passwordReset.id, { 
      status: PasswordResetStatus.USED 
    });

    // Invalidate any other pending password reset requests for this user
    const otherResets = await this.passwordResetRepository.findByUserId(user.id);
    for (const reset of otherResets) {
      if (reset.id !== passwordReset.id && reset.status === PasswordResetStatus.PENDING) {
        await this.passwordResetRepository.update(reset.id, {
          status: PasswordResetStatus.EXPIRED
        });
      }
    }

    return {
      message: 'Password has been reset successfully',
    };
  }
}
