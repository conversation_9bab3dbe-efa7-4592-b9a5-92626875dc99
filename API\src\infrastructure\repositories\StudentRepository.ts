import { eq } from 'drizzle-orm';
import { db } from '../database/connection';
import { students } from '../database/schema';
import { Student, CreateStudentData, UpdateStudentData } from '../../domain/entities/Student';
import { IStudentRepository } from '../../domain/repositories/IStudentRepository';

export class StudentRepository implements IStudentRepository {
  async create(data: CreateStudentData): Promise<Student> {
    const [student] = await db.insert(students).values({
      name: data.name,
      emailId: data.emailId,
      phone: data.phoneNumber, // Map phoneNumber to phone field (which maps to phone_number column)
      collegeId: data.collegeId,
      departmentId: data.departmentId,
      class: data.class || null,
      semester: data.semester || null,
      rollNumber: data.rollNumber || null,
      createdBy: data.createdBy || null,
    }).returning();

    return this.mapToEntity(student);
  }

  async findById(id: string): Promise<Student | null> {
    const [student] = await db.select().from(students).where(eq(students.id, id));
    return student ? this.mapToEntity(student) : null;
  }

  async findByEmail(email: string): Promise<Student | null> {
    const [student] = await db.select().from(students).where(eq(students.emailId, email));
    return student ? this.mapToEntity(student) : null;
  }

  async findByCollegeId(collegeId: string): Promise<Student[]> {
    const results = await db.select().from(students).where(eq(students.collegeId, collegeId));
    return results.map(this.mapToEntity);
  }

  async findByDepartmentId(departmentId: string): Promise<Student[]> {
    const results = await db.select().from(students).where(eq(students.departmentId, departmentId));
    return results.map(this.mapToEntity);
  }

  async findByRollNumber(rollNumber: string): Promise<Student | null> {
    const [student] = await db.select().from(students).where(eq(students.rollNumber, rollNumber));
    return student ? this.mapToEntity(student) : null;
  }

  async findAll(): Promise<Student[]> {
    const results = await db.select().from(students);
    return results.map(this.mapToEntity);
  }

  async update(id: string, data: UpdateStudentData): Promise<Student | null> {
    const updateData: any = {
      modifiedOn: new Date(),
    };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.phoneNumber !== undefined) updateData.phone = data.phoneNumber;
    if (data.collegeId !== undefined) updateData.collegeId = data.collegeId;
    if (data.departmentId !== undefined) updateData.departmentId = data.departmentId;
    if (data.class !== undefined) updateData.class = data.class;
    if (data.semester !== undefined) updateData.semester = data.semester;
    if (data.rollNumber !== undefined) updateData.rollNumber = data.rollNumber;
    if (data.lastSeen !== undefined) updateData.lastSeen = data.lastSeen;
    if (data.modifiedBy !== undefined) updateData.modifiedBy = data.modifiedBy;

    const [updated] = await db.update(students)
      .set(updateData)
      .where(eq(students.id, id))
      .returning();

    return updated ? this.mapToEntity(updated) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(students).where(eq(students.id, id));
    return result.rowCount > 0;
  }

  private mapToEntity(row: any): Student {
    return {
      id: row.id,
      name: row.name,
      emailId: row.emailId,
      phoneNumber: row.phone,
      collegeId: row.collegeId,
      departmentId: row.departmentId,
      class: row.class,
      semester: row.semester,
      rollNumber: row.rollNumber,
      lastSeen: row.lastSeen,
      createdOn: row.createdOn,
      modifiedOn: row.modifiedOn,
      createdBy: row.createdBy,
      modifiedBy: row.modifiedBy,
    };
  }
}
