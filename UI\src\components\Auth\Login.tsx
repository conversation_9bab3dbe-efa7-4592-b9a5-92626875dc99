import React, { useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import loginbg from '../../assets/loginbg.jpg';
import logo from '../../assets/RMKCollegelogo.gif';
import AboutDialog from '../UI/AboutDialog';

type LoginType = 'student' | 'faculty' | null;

const Login: React.FC = () => {

  const [loginType, setLoginType] = useState<LoginType>('student');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showAbout, setShowAbout] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const user = await login(email, password);
      if (!user) {
        setError('Invalid email or password');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen w-full flex overflow-hidden">
      {/* Left Section */}
      <div className="w-full md:w-1/2 flex flex-col justify-between px-8 md:px-20 py-8">
        <div className="flex justify-start">
          <img src={logo} alt="Logo" className="w-20 h-auto" />
        </div>

        <div className="mt-6 max-w-md w-full">
          <div className="flex gap-4 mb-6">
            <button
              onClick={() => setLoginType('student')}
              className={`px-5 py-2 rounded-md text-sm font-medium transition ${loginType === 'student'
                  ? 'bg-green-900 text-white'
                  : 'border border-gray-400 text-black'
                }`}
            >
              Login as Student
            </button>
            <button
              onClick={() => setLoginType('faculty')}
              className={`px-5 py-2 rounded-md text-sm font-medium transition ${loginType === 'faculty'
                  ? 'bg-green-900 text-white'
                  : 'border border-gray-400 text-black'
                }`}
            >
              Login as Faculty
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="text-2xl font-bold text-gray-800">Login</div>
            <div className="text-sm text-gray-600">Welcome Back!</div>

            {error && (
              <div className="bg-red-100 text-red-700 px-4 py-2 rounded-md text-sm">
                {error}
              </div>
            )}

            <div>
              <label className="block text-sm font-medium mb-1">
                {loginType === 'student'
                  ? 'Registration Number/Email'
                  : 'Email Address'}
              </label>
              <input
                type="text"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                placeholder="Enter your email or reg number"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-600 focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="Enter your password"
                  className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-600 focus:outline-none"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600"
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>

            <div className="flex justify-between items-center text-sm">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <span>Remember me</span>
              </label>
              <a href="#" className="text-green-700 hover:underline">
                Forgot Password
              </a>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-green-900 text-white py-2 rounded-md hover:bg-green-800 disabled:opacity-50"
            >
              {loading ? 'Signing In...' : 'Login'}
            </button>
          </form>

          <div className="text-sm text-center mt-6">
            New here?{' '}
            <button className="text-green-700 hover:underline">Sign up</button>
          </div>
        </div>

        <div className="text-xs text-gray-500 text-center mt-8 ml-62">
          ©2025 One Student One Tree •{' '}
          <a href="#" className="hover:underline">
            Need Help? Contact Us
          </a>{' '}
          •{' '}
          <button
            onClick={() => setShowAbout(true)}
            className="hover:underline text-green-700"
          >
            About the initiative
          </button>
        </div>
        <AboutDialog isOpen={showAbout} onClose={() => setShowAbout(false)} />
      </div>

      {/* Right Section */}
      <div className="hidden md:flex md:w-1/2 items-center justify-center bg-white px-6">
        <div className="text-center max-w-md">
          <img src={loginbg} alt="Tree Illustration" className="w-full h-full mb-4" />
        </div>
      </div>
    </div>
  );
};

export default Login;
