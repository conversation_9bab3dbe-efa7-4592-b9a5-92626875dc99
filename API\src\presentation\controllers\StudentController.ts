import { Request, Response } from 'express';
import { CreateStudentProfileUseCase } from '../../application/usecases/student/CreateStudentProfileUseCase';
import { StudentRepository } from '../../infrastructure/repositories/StudentRepository';
import { CollegeRepository } from '../../infrastructure/repositories/CollegeRepository';
import { DepartmentRepository } from '../../infrastructure/repositories/DepartmentRepository';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

export class StudentController {
  private createStudentProfileUseCase: CreateStudentProfileUseCase;
  private studentRepository: StudentRepository;

  constructor() {
    this.studentRepository = new StudentRepository();
    const collegeRepository = new CollegeRepository();
    const departmentRepository = new DepartmentRepository();

    this.createStudentProfileUseCase = new CreateStudentProfileUseCase(
      this.studentRepository,
      collegeRepository,
      departmentRepository
    );
  }

  createStudentProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { name, email, phone, collegeId, departmentId, class: studentClass, semester, rollNumber } = req.body;
    const requesterId = req.user!.id;
    const requesterRole = req.user!.role;
    const requesterCollegeId = req.user!.collegeId;
    const requesterDepartmentId = req.user!.departmentId;

    const student = await this.createStudentProfileUseCase.execute({
      requesterId,
      requesterRole,
      requesterCollegeId,
      requesterDepartmentId,
      studentData: {
        name,
        email,
        phoneNumber: phone,
        collegeId,
        departmentId,
        class: studentClass,
        semester,
        rollNumber,
      },
    });

    res.status(201).json({
      message: 'Student profile created successfully',
      data: student,
    });
  });

  getStudentProfiles = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { collegeId, departmentId } = req.query;
    const userRole = req.user!.role;
    const userCollegeId = req.user!.collegeId;
    const userDepartmentId = req.user!.departmentId;

    let students;

    switch (userRole) {
      case 'admin':
        // Admin can see all student profiles
        students = await this.studentRepository.findAll();
        break;

      case 'principal':
        // Principal can see student profiles in their college
        if (!userCollegeId) {
          return res.status(403).json({ error: 'Principal must be assigned to a college' });
        }
        students = await this.studentRepository.findByCollegeId(userCollegeId);
        break;

      case 'hod':
        // HOD can see student profiles in their college, optionally filtered by department
        if (!userCollegeId) {
          return res.status(403).json({ error: 'HOD must be assigned to a college' });
        }
        if (departmentId && typeof departmentId === 'string') {
          students = await this.studentRepository.findByDepartmentId(departmentId);
        } else {
          students = await this.studentRepository.findByCollegeId(userCollegeId);
        }
        break;

      case 'staff':
        // Staff can only see student profiles in their department
        if (!userDepartmentId) {
          return res.status(403).json({ error: 'Staff must be assigned to a department' });
        }
        students = await this.studentRepository.findByDepartmentId(userDepartmentId);
        break;

      default:
        return res.status(403).json({ error: 'Unauthorized to view student profiles' });
    }

    res.json({
      message: 'Student profiles retrieved successfully',
      data: students,
    });
  });

  getStudentProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const student = await this.studentRepository.findById(id);

    if (!student) {
      return res.status(404).json({ error: 'Student profile not found' });
    }

    // Check permissions
    const userRole = req.user!.role;
    const userCollegeId = req.user!.collegeId;
    const userDepartmentId = req.user!.departmentId;

    switch (userRole) {
      case 'admin':
        // Admin can see any student profile
        break;

      case 'principal':
        if (student.collegeId !== userCollegeId) {
          return res.status(403).json({ error: 'Cannot access student profile from different college' });
        }
        break;

      case 'hod':
        if (student.collegeId !== userCollegeId) {
          return res.status(403).json({ error: 'Cannot access student profile from different college' });
        }
        break;

      case 'staff':
        if (student.departmentId !== userDepartmentId) {
          return res.status(403).json({ error: 'Cannot access student profile from different department' });
        }
        break;

      default:
        return res.status(403).json({ error: 'Unauthorized to view student profile' });
    }

    res.json({
      message: 'Student profile retrieved successfully',
      data: student,
    });
  });

  updateStudentProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { name, phone, class: studentClass, semester, rollNumber } = req.body;

    const existingStudent = await this.studentRepository.findById(id);
    if (!existingStudent) {
      return res.status(404).json({ error: 'Student profile not found' });
    }

    // Check permissions
    const userRole = req.user!.role;
    const userCollegeId = req.user!.collegeId;
    const userDepartmentId = req.user!.departmentId;

    switch (userRole) {
      case 'admin':
        // Admin can update any student profile
        break;

      case 'principal':
        if (existingStudent.collegeId !== userCollegeId) {
          return res.status(403).json({ error: 'Cannot update student profile from different college' });
        }
        break;

      case 'hod':
        if (existingStudent.collegeId !== userCollegeId) {
          return res.status(403).json({ error: 'Cannot update student profile from different college' });
        }
        break;

      case 'staff':
        if (existingStudent.departmentId !== userDepartmentId) {
          return res.status(403).json({ error: 'Cannot update student profile from different department' });
        }
        break;

      default:
        return res.status(403).json({ error: 'Unauthorized to update student profile' });
    }

    const updatedStudent = await this.studentRepository.update(id, {
      name,
      phoneNumber: phone,
      class: studentClass,
      semester,
      rollNumber,
      modifiedBy: req.user!.id,
    });

    if (!updatedStudent) {
      return res.status(404).json({ error: 'Failed to update student profile' });
    }

    res.json({
      message: 'Student profile updated successfully',
      data: updatedStudent,
    });
  });

  getStudentProfileByEmail = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { email } = req.params;
    const student = await this.studentRepository.findByEmail(email);

    if (!student) {
      return res.status(404).json({ error: 'Student profile not found' });
    }

    res.json({
      message: 'Student profile retrieved successfully',
      data: student,
    });
  });

  deleteStudentProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const existingStudent = await this.studentRepository.findById(id);
    if (!existingStudent) {
      return res.status(404).json({ error: 'Student profile not found' });
    }

    // Check permissions
    const userRole = req.user!.role;
    const userCollegeId = req.user!.collegeId;
    const userDepartmentId = req.user!.departmentId;

    switch (userRole) {
      case 'admin':
        // Admin can delete any student profile
        break;

      case 'principal':
        if (existingStudent.collegeId !== userCollegeId) {
          return res.status(403).json({ error: 'Cannot delete student profile from different college' });
        }
        break;

      case 'hod':
        if (existingStudent.collegeId !== userCollegeId) {
          return res.status(403).json({ error: 'Cannot delete student profile from different college' });
        }
        break;

      case 'staff':
        if (existingStudent.departmentId !== userDepartmentId) {
          return res.status(403).json({ error: 'Cannot delete student profile from different department' });
        }
        break;

      default:
        return res.status(403).json({ error: 'Unauthorized to delete student profile' });
    }

    const deleted = await this.studentRepository.delete(id);
    if (!deleted) {
      return res.status(404).json({ error: 'Failed to delete student profile' });
    }

    res.json({
      message: 'Student profile deleted successfully',
    });
  });
}
