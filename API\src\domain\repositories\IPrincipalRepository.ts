import { Principal, CreatePrincipalData, UpdatePrincipalData } from '../entities/Principal';

export interface IPrincipalRepository {
  create(data: CreatePrincipalData): Promise<Principal>;
  findById(id: string): Promise<Principal | null>;
  findByEmail(email: string): Promise<Principal | null>;
  findByCollegeId(collegeId: string): Promise<Principal[]>;
  findAll(): Promise<Principal[]>;
  update(id: string, data: UpdatePrincipalData): Promise<Principal | null>;
  delete(id: string): Promise<boolean>;
}
