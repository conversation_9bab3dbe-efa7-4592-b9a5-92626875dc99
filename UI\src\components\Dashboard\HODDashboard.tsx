import React, { useState, useEffect } from 'react';
import { Users, GraduationCap, UserCheck, BookOpen, TrendingUp, Calendar } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { User, Department } from '../../types';

const HODDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    departmentStaff: 0,
    departmentStudents: 0,
    pendingRequests: 0,
    activeClasses: 0
  });
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [usersRes, departmentsRes] = await Promise.all([
        fetch('/src/data/users.json'),
        fetch('/src/data/departments.json')
      ]);
      
      const users: User[] = await usersRes.json();
      const departments: Department[] = await departmentsRes.json();

      const department = departments.find(d => d.id === user?.departmentId);
      const departmentUsers = users.filter(u => u.departmentId === user?.departmentId);

      setStats({
        departmentStaff: departmentUsers.filter(u => u.role === 'staff').length,
        departmentStudents: departmentUsers.filter(u => u.role === 'student').length,
        pendingRequests: 0,
        activeClasses: 5 // Mock data
      });

      // Create recent activity
      const activity = departmentUsers
        .filter(u => u.createdAt)
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)
        .map(u => ({
          type: 'user_joined',
          message: `${u.name} joined as ${u.role}`,
          timestamp: u.createdAt,
          status: u.status
        }));

      setRecentActivity(activity);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const statCards = [
    {
      title: 'Department Staff',
      value: stats.departmentStaff,
      icon: Users,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Department Students',
      value: stats.departmentStudents,
      icon: GraduationCap,
      color: 'bg-green-500',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Active Classes',
      value: stats.activeClasses,
      icon: BookOpen,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Pending Requests',
      value: stats.pendingRequests,
      icon: UserCheck,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">HOD Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's your department overview.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className={`${stat.bgColor} p-6 rounded-xl border border-gray-100`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-xl border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            <TrendingUp className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.status === 'active' ? 'bg-green-500' : 'bg-orange-500'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(activity.timestamp).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white p-6 rounded-xl border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
            <Calendar className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-3">
            <button className="w-full p-4 text-left bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
              <div className="flex items-center space-x-3">
                <Users className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">Invite Staff</p>
                  <p className="text-sm text-green-600">Send invitations to department staff</p>
                </div>
              </div>
            </button>
            
            <button className="w-full p-4 text-left bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
              <div className="flex items-center space-x-3">
                <GraduationCap className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">View Students</p>
                  <p className="text-sm text-blue-600">Monitor department students</p>
                </div>
              </div>
            </button>
            
            <button className="w-full p-4 text-left bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
              <div className="flex items-center space-x-3">
                <UserCheck className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-900">Review Requests</p>
                  <p className="text-sm text-orange-600">Approve student registrations</p>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HODDashboard;