import React, { useState } from 'react';
import {
  Users,
  TreePine,
  CheckCircle,
  Clock,
  XCircle,
  Calendar,
  Eye,
  UserCheck,
  TrendingUp
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import TreePlantingList from '../TreePlanting/TreePlantingList';
import { useTreePlantingStats } from '../../hooks/useTreePlantings';
import { useAuth } from '../../hooks/useAuth';
import { useUsers } from '../../hooks/useUserQueries';

const StaffDashboard: React.FC = () => {
  const { user } = useAuth();
  const { data: treePlantingStats, loading: treePlantingLoading } = useTreePlantingStats();
  const { data: students, loading: studentsLoading } = useUsers({ role: 'student' });
  const [selectedView, setSelectedView] = useState<'overview' | 'students' | 'plantings'>('overview');

  const loading = treePlantingLoading || studentsLoading;

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Staff Dashboard</h1>
          <p className="text-gray-600">
            Monitor and verify student tree planting activities
          </p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Calendar className="h-4 w-4" />
          <span>Department: {user?.departmentId || 'Not assigned'}</span>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-md p-1">
        <div className="flex space-x-1">
          {[
            { key: 'overview', label: 'Overview', icon: TrendingUp },
            { key: 'students', label: 'Students', icon: Users },
            { key: 'plantings', label: 'Tree Plantings', icon: TreePine },
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setSelectedView(key as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                selectedView === key
                  ? 'bg-green-600 text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Overview Tab */}
      {selectedView === 'overview' && (
        <div className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatisticsCard
              title="Total Students"
              value={students?.length || 0}
              icon={Users}
              color="blue"
              subtitle="In your department"
            />
            <StatisticsCard
              title="Trees Planted"
              value={treePlantingStats?.totalTrees || 0}
              icon={TreePine}
              color="green"
              subtitle="By your students"
            />
            <StatisticsCard
              title="Pending Review"
              value={treePlantingStats?.pendingVerification || 0}
              icon={Clock}
              color="yellow"
              subtitle="Awaiting verification"
            />
            <StatisticsCard
              title="Approved"
              value={treePlantingStats?.approved || 0}
              icon={CheckCircle}
              color="green"
              subtitle="Verified plantings"
            />
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => setSelectedView('plantings')}
                className="p-6 bg-yellow-50 border-2 border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="bg-yellow-600 p-3 rounded-full">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-yellow-900">Review Pending Submissions</h3>
                    <p className="text-sm text-yellow-600">
                      {treePlantingStats?.pendingVerification || 0} submissions awaiting review
                    </p>
                  </div>
                </div>
              </button>

              <button
                onClick={() => setSelectedView('students')}
                className="p-6 bg-blue-50 border-2 border-blue-200 rounded-lg hover:bg-blue-100 transition-colors text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-600 p-3 rounded-full">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-blue-900">Manage Students</h3>
                    <p className="text-sm text-blue-600">
                      View and manage {students?.length || 0} students
                    </p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Performance Overview */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Department Performance</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                  <TreePine className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900">Participation Rate</h3>
                <p className="text-2xl font-bold text-green-600">
                  {students?.length ? Math.round((treePlantingStats?.totalTrees || 0) / students.length * 100) : 0}%
                </p>
              </div>

              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900">Approval Rate</h3>
                <p className="text-2xl font-bold text-blue-600">
                  {treePlantingStats?.totalTrees ?
                    Math.round((treePlantingStats.approved / treePlantingStats.totalTrees) * 100) : 0}%
                </p>
              </div>

              <div className="text-center">
                <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                  <Clock className="h-8 w-8 text-yellow-600" />
                </div>
                <h3 className="font-semibold text-gray-900">Avg. Review Time</h3>
                <p className="text-2xl font-bold text-yellow-600">2.3 days</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Students Tab */}
      {selectedView === 'students' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Students in Your Department</h2>
            <UserCheck className="h-5 w-5 text-blue-600" />
          </div>

          {!students || students.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No students found in your department</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {students.map((student: any) => (
                <div key={student.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="bg-blue-100 rounded-full w-10 h-10 flex items-center justify-center">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{student.name}</h3>
                      <p className="text-sm text-gray-500">{student.email}</p>
                    </div>
                  </div>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Class:</span> {student.class || 'Not specified'}</p>
                    <p><span className="font-medium">Semester:</span> {student.semester || 'Not specified'}</p>
                    <p><span className="font-medium">Roll No:</span> {student.rollNumber || 'Not specified'}</p>
                  </div>
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                      View Tree Plantings
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Tree Plantings Tab */}
      {selectedView === 'plantings' && (
        <TreePlantingList showFilters={true} />
      )}
    </div>
  );
};

export default StaffDashboard;