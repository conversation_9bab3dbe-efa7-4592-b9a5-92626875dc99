import React, { useState } from 'react';
import {
  TreePine,
  Upload,
  CheckCircle,
  Clock,
  XCircle,
  Calendar,
  Award,
  Target,
  TrendingUp
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import TreePlantingUpload from '../TreePlanting/TreePlantingUpload';
import TreePlantingList from '../TreePlanting/TreePlantingList';
import { useTreePlantingStats } from '../../hooks/useTreePlantings';
import { useAuth } from '../../hooks/useAuth';

const StudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const { data: stats, loading } = useTreePlantingStats();
  const [showUploadForm, setShowUploadForm] = useState(false);

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const currentSemester = user?.semester || '1';
  const currentYear = new Date().getFullYear();
  const academicYear = `${currentYear}-${currentYear + 1}`;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Tree Planting Journey</h1>
          <p className="text-gray-600">
            Welcome back, {user?.name}! Track your environmental impact.
          </p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Calendar className="h-4 w-4" />
          <span>Semester {currentSemester} • {academicYear}</span>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatisticsCard
          title="Trees Planted"
          value={stats?.totalTrees || 0}
          icon={TreePine}
          color="green"
          subtitle="Total contribution"
        />
        <StatisticsCard
          title="Approved Plantings"
          value={stats?.approved || 0}
          icon={CheckCircle}
          color="green"
          subtitle="Verified trees"
        />
        <StatisticsCard
          title="Pending Review"
          value={stats?.pending || 0}
          icon={Clock}
          color="yellow"
          subtitle="Awaiting verification"
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
          <Upload className="h-5 w-5 text-green-600" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setShowUploadForm(true)}
            className="p-6 bg-green-50 border-2 border-green-200 rounded-lg hover:bg-green-100 transition-colors text-left"
          >
            <div className="flex items-center space-x-4">
              <div className="bg-green-600 p-3 rounded-full">
                <Upload className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-green-900">Upload Tree Planting</h3>
                <p className="text-sm text-green-600">
                  Add your latest tree planting record
                </p>
              </div>
            </div>
          </button>

          <div className="p-6 bg-blue-50 border-2 border-blue-200 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 p-3 rounded-full">
                <Target className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-blue-900">Semester Goal</h3>
                <p className="text-sm text-blue-600">
                  Plant 1 tree this semester
                </p>
                <div className="mt-2 bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(((stats?.totalTrees || 0) / 1) * 100, 100)}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Overview */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Your Impact</h2>
          <TrendingUp className="h-5 w-5 text-green-600" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-3">
              <TreePine className="h-10 w-10 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Environmental Impact</h3>
            <p className="text-sm text-gray-600 mt-1">
              Your trees will absorb approximately{' '}
              <span className="font-semibold text-green-600">
                {((stats?.approved || 0) * 48).toLocaleString()} lbs
              </span>{' '}
              of CO₂ over their lifetime
            </p>
          </div>

          <div className="text-center">
            <div className="bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-3">
              <Award className="h-10 w-10 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Achievement Level</h3>
            <p className="text-sm text-gray-600 mt-1">
              {stats?.approved === 0 ? 'Getting Started' :
               stats?.approved < 3 ? 'Tree Planter' :
               stats?.approved < 6 ? 'Eco Warrior' :
               stats?.approved < 10 ? 'Green Champion' : 'Environmental Hero'}
            </p>
          </div>

          <div className="text-center">
            <div className="bg-yellow-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-3">
              <Calendar className="h-10 w-10 text-yellow-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Consistency</h3>
            <p className="text-sm text-gray-600 mt-1">
              {stats?.totalTrees > 0 ? 'Great job!' : 'Start your journey today!'}
            </p>
          </div>
        </div>
      </div>

      {/* Upload Form Modal */}
      {showUploadForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <TreePlantingUpload
              onSuccess={() => {
                setShowUploadForm(false);
                // Refresh the page or refetch data
                window.location.reload();
              }}
              onCancel={() => setShowUploadForm(false)}
            />
          </div>
        </div>
      )}

      {/* Tree Plantings List */}
      <TreePlantingList studentId={user?.id} />
    </div>
  );
};

export default StudentDashboard;