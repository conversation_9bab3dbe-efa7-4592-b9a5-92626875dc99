const postgres = require('postgres');
require('dotenv').config();

async function checkMigrations() {
  const sql = postgres({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
  });

  try {
    console.log('✅ Connected to database');

    // Check if drizzle migrations table exists
    const migrationTableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'drizzle' 
        AND table_name = '__drizzle_migrations'
      )
    `;
    
    console.log('📋 Drizzle migrations table exists:', migrationTableExists[0].exists);

    if (migrationTableExists[0].exists) {
      // Get applied migrations
      const appliedMigrations = await sql`
        SELECT * FROM drizzle.__drizzle_migrations 
        ORDER BY created_at
      `;
      
      console.log('🔄 Applied migrations:');
      appliedMigrations.forEach(migration => {
        console.log(`  - ${migration.hash}: ${migration.created_at}`);
      });
    }

    // Check if the users table has the expected columns
    const usersColumns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_schema = 'treedev' AND table_name = 'users'
      ORDER BY ordinal_position
    `;

    console.log('\n👤 Current users table columns:');
    usersColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type}`);
    });

    // Expected columns from schema
    const expectedColumns = [
      'id', 'email', 'name', 'password', 'role', 'phone', 'status', 
      'college_id', 'department_id', 'class_in_charge', 'class', 
      'semester', 'roll_number', 'created_at', 'updated_at', 'last_login'
    ];

    console.log('\n🎯 Expected columns from schema:');
    expectedColumns.forEach(col => {
      const exists = usersColumns.some(dbCol => dbCol.column_name === col);
      console.log(`  - ${col}: ${exists ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sql.end();
  }
}

checkMigrations();
