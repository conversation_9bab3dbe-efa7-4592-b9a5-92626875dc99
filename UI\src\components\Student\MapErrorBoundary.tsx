import React, { Component, ReactNode } from 'react';
import { MapPin } from 'lucide-react';

interface Props {
  children: ReactNode;
  coordinates?: [number, number];
}

interface State {
  hasError: boolean;
}

class MapErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Map component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const { coordinates } = this.props;
      const [lat, lng] = coordinates || [0, 0];
      
      return (
        <div className="w-full h-full rounded-lg border border-gray-300 bg-green-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <MapPin className="w-4 h-4 text-white" />
            </div>
            <p className="text-xs text-green-700 font-medium">Location Pinned</p>
            {coordinates && (
              <p className="text-xs text-green-600">{lat.toFixed(4)}, {lng.toFixed(4)}</p>
            )}
            <button
              onClick={() => this.setState({ hasError: false })}
              className="text-xs text-green-600 hover:text-green-700 underline mt-1"
            >
              Retry Map
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default MapErrorBoundary;
