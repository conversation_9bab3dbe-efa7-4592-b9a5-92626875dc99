import { db } from '../connection';
import { sql } from 'drizzle-orm';

export async function addCoursesTable() {
  console.log('Creating courses table...');

  try {
    // Create course_status enum if it doesn't exist
    await db.execute(sql`
      DO $$ BEGIN
        CREATE TYPE treedev.course_status AS ENUM ('active', 'inactive');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create courses table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS treedev.courses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        code VARCHAR(20) NOT NULL,
        department_id UUID NOT NULL,
        college_id UUID NOT NULL,
        credits INTEGER NOT NULL,
        semester VARCHAR(20) NOT NULL,
        description TEXT,
        status treedev.course_status NOT NULL DEFAULT 'active',
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        
        -- Foreign key constraints
        CONSTRAINT fk_courses_department 
          FOREIGN KEY (department_id) 
          REFERENCES treedev.departments(id) 
          ON DELETE CASCADE,
        
        CONSTRAINT fk_courses_college 
          FOREIGN KEY (college_id) 
          REFERENCES treedev.colleges(id) 
          ON DELETE CASCADE,
        
        -- Unique constraint for course code within department
        CONSTRAINT unique_course_code_per_department 
          UNIQUE (code, department_id)
      );
    `);

    // Create indexes for better performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_courses_department_id ON treedev.courses(department_id);
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_courses_college_id ON treedev.courses(college_id);
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_courses_status ON treedev.courses(status);
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_courses_semester ON treedev.courses(semester);
    `);

    console.log('Courses table created successfully!');
  } catch (error) {
    console.error('Error creating courses table:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  addCoursesTable()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
