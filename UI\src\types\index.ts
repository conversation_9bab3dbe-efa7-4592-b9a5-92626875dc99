export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'principal' | 'hod' | 'staff' | 'student';
  phone: string;
  status: 'active' | 'inactive' | 'pending';
  collegeId: string | null;
  departmentId: string | null;
  classInCharge?: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
  createdAt: string;
  lastLogin?: string;
}

export interface College {
  id: string;
  name: string;
  code: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  established: string;
  principalId: string | null;
  status: 'active' | 'inactive';
  departments: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Department {
  id: string;
  name: string;
  code: string;
  collegeId: string;
  hodId: string | null;
  totalStudents: number;
  totalStaff: number;
  established: string;
}

export interface Course {
  id: string;
  name: string;
  code: string;
  departmentId: string;
  collegeId: string;
  credits: number;
  semester: string;
  description: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface Invitation {
  id: string;
  email: string;
  role: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  sentBy: string;
  sentAt: string;
  acceptedAt?: string;
  expiresAt: string;
  collegeId: string;
  departmentId?: string;
  token: string;
  createdAt: string;
  updatedAt: string;
}

export interface RegistrationRequest {
  id: string;
  email: string;
  name: string;
  role: string;
  phone: string;
  status: 'pending' | 'approved' | 'rejected';
  requestedAt: string;
  collegeId: string | null;
  departmentId?: string;
  class?: string;
  rollNumber?: string;
  collegeName?: string;
  reviewedBy: string | null;
}

export interface TreePlanting {
  id: string;
  studentId: string;
  semester: string;
  academicYear: string;
  plantingDate: string;
  location: string;
  treeType?: string;
  description?: string;
  mediaUrl: string;
  mediaType: 'image' | 'video';
  verificationStatus: 'pending' | 'approved' | 'rejected';
  verifiedBy?: string;
  verifiedAt?: string;
  verificationNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ApiError {
  error: string;
  details?: any;
}