CREATE TYPE "treedev"."course_status" AS ENUM('active', 'inactive');--> statement-breakpoint
CREATE TYPE "public"."password_reset_status" AS ENUM('pending', 'used', 'expired');--> statement-breakpoint
CREATE TABLE "treedev"."courses" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"code" varchar(20) NOT NULL,
	"department_id" uuid NOT NULL,
	"college_id" uuid NOT NULL,
	"credits" integer NOT NULL,
	"semester" varchar(20) NOT NULL,
	"description" text,
	"status" "treedev"."course_status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "treedev"."password_resets" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"email" varchar(255) NOT NULL,
	"token" varchar(255) NOT NULL,
	"otp" varchar(6) NOT NULL,
	"status" "password_reset_status" DEFAULT 'pending' NOT NULL,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "password_resets_token_unique" UNIQUE("token")
);
