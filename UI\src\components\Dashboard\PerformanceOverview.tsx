import React from 'react';
import { TrendingUp, TrendingDown, Award, AlertTriangle } from 'lucide-react';

interface PerformanceData {
  id: string;
  name: string;
  totalStudents: number;
  treesPlanted: number;
  completionRate: number;
  participationRate: number;
}

interface PerformanceOverviewProps {
  data: PerformanceData[];
  title: string;
  type: 'college' | 'department' | 'class';
}

export default function PerformanceOverview({ data, title, type }: PerformanceOverviewProps) {
  // Sort by completion rate to get top and lowest performers
  const sortedData = [...data].sort((a, b) => b.completionRate - a.completionRate);
  const topPerformers = sortedData.slice(0, 3);
  const lowestPerformers = sortedData.slice(-3).reverse();

  const getPerformanceColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600 bg-green-50';
    if (rate >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getPerformanceIcon = (rate: number) => {
    if (rate >= 80) return <TrendingUp className="h-4 w-4" />;
    if (rate >= 60) return <Award className="h-4 w-4" />;
    return <TrendingDown className="h-4 w-4" />;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">{title}</h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performers */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Award className="h-5 w-5 text-green-600" />
            <h4 className="font-medium text-green-900">Top Performers</h4>
          </div>
          <div className="space-y-3">
            {topPerformers.map((item, index) => (
              <div key={item.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    'bg-orange-400'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{item.name}</p>
                    <p className="text-sm text-gray-600">
                      {item.treesPlanted} trees • {item.totalStudents} students
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1">
                    {getPerformanceIcon(item.completionRate)}
                    <span className="font-semibold text-green-600">
                      {item.completionRate.toFixed(1)}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">completion rate</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Lowest Performers */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h4 className="font-medium text-red-900">Needs Attention</h4>
          </div>
          <div className="space-y-3">
            {lowestPerformers.map((item, index) => (
              <div key={item.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-red-500 flex items-center justify-center text-white font-bold">
                    {sortedData.length - 2 + index}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{item.name}</p>
                    <p className="text-sm text-gray-600">
                      {item.treesPlanted} trees • {item.totalStudents} students
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1">
                    {getPerformanceIcon(item.completionRate)}
                    <span className="font-semibold text-red-600">
                      {item.completionRate.toFixed(1)}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">completion rate</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
