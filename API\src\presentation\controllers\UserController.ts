import { Request, Response } from 'express';
import { GetUsersUseCase } from '../../application/usecases/user/GetUsersUseCase';
import { CreateUserUseCase } from '../../application/usecases/user/CreateUserUseCase';
import { UpdateUserUseCase } from '../../application/usecases/user/UpdateUserUseCase';
import { UserRepository } from '../../infrastructure/repositories/UserRepository';
import { CollegeRepository } from '../../infrastructure/repositories/CollegeRepository';
import { DepartmentRepository } from '../../infrastructure/repositories/DepartmentRepository';
import { AuthService } from '../../infrastructure/services/AuthService';
import { EmailService } from '../../infrastructure/services/EmailService';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { UserRole } from '../../domain/entities/User';

export class UserController {
  private getUsersUseCase: GetUsersUseCase;
  private createUserUseCase: CreateUserUseCase;
  private updateUserUseCase: UpdateUserUseCase;
  private userRepository: UserRepository;
  private collegeRepository: CollegeRepository;
  private departmentRepository: DepartmentRepository;
  private authService: AuthService;
  private emailService: EmailService;

  constructor() {
    this.userRepository = new UserRepository();
    this.collegeRepository = new CollegeRepository();
    this.departmentRepository = new DepartmentRepository();
    this.authService = new AuthService();
    this.emailService = new EmailService();
    this.getUsersUseCase = new GetUsersUseCase(this.userRepository);
    this.createUserUseCase = new CreateUserUseCase(
      this.userRepository,
      this.collegeRepository,
      this.departmentRepository,
      this.authService
    );
    this.updateUserUseCase = new UpdateUserUseCase(this.userRepository);
  }

  getUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page = '1', limit = '10', search, role, status } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    const filters = {
      limit: parseInt(limit as string),
      offset,
      search: search as string,
      role: role as any,
      status: status as string,
    };

    const users = await this.getUsersUseCase.execute({
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
      filters,
    });

    res.json({
      message: 'Users retrieved successfully',
      data: users,
    });
  });

  getUserById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const user = await this.userRepository.findById(id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if requester can access this user
    const canAccess = this.canAccessUser(req.user!, user);
    if (!canAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      message: 'User retrieved successfully',
      data: {
        ...user,
        password: undefined,
      },
    });
  });

  createUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userData = req.body;

    const user = await this.createUserUseCase.execute({
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
      userData,
    });

    res.status(201).json({
      message: 'User created successfully',
      data: user,
    });
  });

  updateUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;

    const updatedUser = await this.updateUserUseCase.execute({
      userId: id,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
      updateData,
    });

    res.json({
      message: 'User updated successfully',
      data: updatedUser,
    });
  });

  deleteUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const user = await this.userRepository.findById(id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Cannot delete yourself
    if (user.id === req.user!.id) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    // Role-based permission checks
    if (req.user!.role === 'admin') {
      // Admin can delete any user
    } else if (req.user!.role === 'principal') {
      // Principal can delete users in their college
      if (!req.user!.collegeId || user.collegeId !== req.user!.collegeId) {
        return res.status(403).json({ error: 'Principal can only delete users in their own college' });
      }
      // Principal cannot delete admin or other principals
      if (user.role === 'admin' || user.role === 'principal') {
        return res.status(403).json({ error: 'Principal cannot delete admin or principal users' });
      }
    } else {
      return res.status(403).json({ error: 'Insufficient permissions to delete users' });
    }

    const deleted = await this.userRepository.delete(id);
    if (!deleted) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: 'User deleted successfully',
    });
  });

  getUserStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { role, collegeId, departmentId } = req.user!;

    let stats: any = {};

    switch (role) {
      case 'admin':
        stats = {
          totalUsers: await this.userRepository.countByRole(UserRole.STUDENT),
          totalAdmins: await this.userRepository.countByRole(UserRole.ADMIN),
          totalPrincipals: await this.userRepository.countByRole(UserRole.PRINCIPAL),
          totalHODs: await this.userRepository.countByRole(UserRole.HOD),
          totalStaff: await this.userRepository.countByRole(UserRole.STAFF),
          totalStudents: await this.userRepository.countByRole(UserRole.STUDENT),
        };
        break;

      case 'principal':
        if (collegeId) {
          stats = {
            totalUsers: await this.userRepository.countByCollege(collegeId),
            totalHODs: await this.userRepository.findByCollege(collegeId).then(users => 
              users.filter(u => u.role === 'hod').length
            ),
            totalStaff: await this.userRepository.findByCollege(collegeId).then(users => 
              users.filter(u => u.role === 'staff').length
            ),
            totalStudents: await this.userRepository.findByCollege(collegeId).then(users => 
              users.filter(u => u.role === 'student').length
            ),
          };
        }
        break;

      case 'hod':
        if (departmentId) {
          stats = {
            totalUsers: await this.userRepository.countByDepartment(departmentId),
            totalStaff: await this.userRepository.findByDepartment(departmentId).then(users => 
              users.filter(u => u.role === 'staff').length
            ),
            totalStudents: await this.userRepository.findByDepartment(departmentId).then(users => 
              users.filter(u => u.role === 'student').length
            ),
          };
        }
        break;

      case 'staff':
        if (departmentId) {
          stats = {
            totalStudents: await this.userRepository.findByDepartment(departmentId).then(users => 
              users.filter(u => u.role === 'student').length
            ),
          };
        }
        break;

      default:
        return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      message: 'User statistics retrieved successfully',
      data: stats,
    });
  });

  private canAccessUser(requester: any, targetUser: any): boolean {
    switch (requester.role) {
      case 'admin':
        return true;

      case 'principal':
        return targetUser.collegeId === requester.collegeId;

      case 'hod':
        return targetUser.departmentId === requester.departmentId;

      case 'staff':
        return targetUser.departmentId === requester.departmentId && targetUser.role === 'student';

      case 'student':
        return targetUser.id === requester.id;

      default:
        return false;
    }
  }

  sendLoginDetails = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { email, name, password, role } = req.body;

    if (!email || !name || !password || !role) {
      return res.status(400).json({
        error: 'Missing required fields: email, name, password, role'
      });
    }

    try {
      // Get user's college and department information
      const user = await this.userRepository.findByEmail(email);
      if (!user) {
        return res.status(404).json({
          error: 'User not found'
        });
      }

      let college = null;
      let department = null;

      if (user.collegeId) {
        college = await this.collegeRepository.findById(user.collegeId);
      }

      if (user.departmentId) {
        department = await this.departmentRepository.findById(user.departmentId);
      }

      // Send login details email
      const emailSent = await this.emailService.sendLoginDetails(email, {
        name,
        email,
        password,
        role,
        collegeName: college?.name || 'Unknown College',
        departmentName: department?.name,
        loginUrl: `${process.env.FRONTEND_URL}/login`,
      });

      if (emailSent) {
        res.json({
          message: 'Login details sent successfully',
          data: { email, sent: true }
        });
      } else {
        res.status(500).json({
          error: 'Failed to send login details email'
        });
      }
    } catch (error) {
      console.error('Error sending login details:', error);
      res.status(500).json({
        error: 'Internal server error while sending login details'
      });
    }
  });
}
