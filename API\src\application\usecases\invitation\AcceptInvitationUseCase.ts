import { IInvitationRepository } from '../../../domain/repositories/IInvitationRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { IAuthService } from '../../../domain/services/IAuthService';
import { IEmailService } from '../../../domain/services/IEmailService';
import { CreateUserData, User, UserStatus } from '../../../domain/entities/User';
import { InvitationStatus } from '../../../domain/entities/Invitation';
import { ValidationError, NotFoundError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface AcceptInvitationRequest {
  token: string;
  name: string;
  password: string;
  phone: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
}

export class AcceptInvitationUseCase {
  constructor(
    private invitationRepository: IInvitationRepository,
    private userRepository: IUserRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository,
    private authService: IAuthService,
    private emailService: IEmailService
  ) {}

  async execute(request: AcceptInvitationRequest): Promise<User> {
    const { token, name, password, phone, class: userClass, semester, rollNumber } = request;

    console.log('🔍 AcceptInvitationUseCase: Starting with token:', token);

    // Find invitation by token
    const invitation = await this.invitationRepository.findByToken(token);
    if (!invitation) {
      console.log('❌ AcceptInvitationUseCase: Invitation not found for token:', token);
      throw new NotFoundError('Invalid invitation token');
    }

    console.log('✅ AcceptInvitationUseCase: Found invitation:', {
      id: invitation.id,
      email: invitation.email,
      status: invitation.status,
      expiresAt: invitation.expiresAt
    });

    // Check invitation status and expiry
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation has already been processed');
    }

    if (invitation.expiresAt < new Date()) {
      // Mark as expired
      await this.invitationRepository.update(invitation.id, { status: 'expired' as any });
      throw new ValidationError('Invitation has expired');
    }

    // Check if OTP has been verified
    if (!invitation.otpVerified) {
      console.log('❌ AcceptInvitationUseCase: OTP not verified for invitation:', invitation.id);
      throw new ValidationError('Email verification required. Please verify your email with the OTP sent to you.');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(invitation.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await this.authService.hashPassword(password);

    // Create user data
    const userData: CreateUserData = {
      email: invitation.email,
      name,
      password: hashedPassword,
      role: invitation.role as any,
      phone,
      collegeId: invitation.collegeId,
      departmentId: invitation.departmentId || undefined,
      class: userClass,
      semester,
      rollNumber,
    };

    // Validate role-specific requirements
    this.validateRoleRequirements(userData);

    // Create user
    const user = await this.userRepository.create(userData);

    // Update user status to active
    await this.userRepository.update(user.id, { status: UserStatus.ACTIVE });

    // Mark invitation as accepted
    console.log('🔄 AcceptInvitationUseCase: Updating invitation status to accepted for ID:', invitation.id);
    const updatedInvitation = await this.invitationRepository.update(invitation.id, {
      status: 'accepted' as any,
      acceptedAt: new Date(),
    });

    console.log('✅ AcceptInvitationUseCase: Invitation updated:', updatedInvitation ? {
      id: updatedInvitation.id,
      status: updatedInvitation.status,
      acceptedAt: updatedInvitation.acceptedAt
    } : 'null');

    // Verify the update was successful
    if (!updatedInvitation || updatedInvitation.status !== 'accepted') {
      console.log('❌ AcceptInvitationUseCase: Failed to update invitation status');
      throw new Error('Failed to update invitation status');
    }

    // Get college and department information for welcome email
    const college = await this.collegeRepository.findById(invitation.collegeId);
    let department = null;
    if (invitation.departmentId) {
      department = await this.departmentRepository.findById(invitation.departmentId);
    }

    // Send welcome email
    await this.emailService.sendWelcome(user.email, {
      name: user.name,
      role: user.role,
      collegeName: college?.name || 'Unknown College',
      departmentName: department?.name,
      loginUrl: `${process.env.FRONTEND_URL}/login`,
    });

    // Return user without password
    return {
      ...user,
      password: undefined,
    };
  }

  private validateRoleRequirements(userData: CreateUserData): void {
    // Student-specific validations
    if (userData.role === 'student') {
      if (!userData.departmentId) {
        throw new ValidationError('Department is required for students');
      }
      if (!userData.class) {
        throw new ValidationError('Class is required for students');
      }
      if (!userData.semester) {
        throw new ValidationError('Semester is required for students');
      }
      if (!userData.rollNumber) {
        throw new ValidationError('Roll number is required for students');
      }
    }

    // Staff-specific validations
    if (userData.role === 'staff') {
      if (!userData.departmentId) {
        throw new ValidationError('Department is required for staff');
      }
    }

    // HOD-specific validations
    if (userData.role === 'hod') {
      if (!userData.departmentId) {
        throw new ValidationError('Department is required for HOD');
      }
    }

    // Principal-specific validations
    if (userData.role === 'principal') {
      if (!userData.collegeId) {
        throw new ValidationError('College is required for principal');
      }
    }
  }
}
