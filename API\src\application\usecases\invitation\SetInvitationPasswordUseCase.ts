import { IInvitationRepository } from '../../../domain/repositories/IInvitationRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { IAuthService } from '../../../domain/services/IAuthService';
import { IEmailService } from '../../../domain/services/IEmailService';
import { User, UserStatus } from '../../../domain/entities/User';
import { CreateUserData } from '../../../domain/entities/User';
import { NotFoundError, ValidationError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface SetInvitationPasswordRequest {
  token: string;
  name: string;
  password: string;
  phone: string;
}

export class SetInvitationPasswordUseCase {
  constructor(
    private invitationRepository: IInvitationRepository,
    private userRepository: IUserRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository,
    private authService: IAuthService,
    private emailService: IEmailService
  ) {}

  async execute(request: SetInvitationPasswordRequest): Promise<User> {
    const { token, name, password, phone } = request;

    console.log('🔍 SetInvitationPasswordUseCase: Starting with token:', token);

    // Find invitation by token
    const invitation = await this.invitationRepository.findByToken(token);
    if (!invitation) {
      console.log('❌ SetInvitationPasswordUseCase: Invitation not found for token:', token);
      throw new NotFoundError('Invalid invitation token');
    }

    console.log('✅ SetInvitationPasswordUseCase: Found invitation:', {
      id: invitation.id,
      email: invitation.email,
      status: invitation.status,
      role: invitation.role,
      expiresAt: invitation.expiresAt
    });

    // Check invitation status and expiry
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation has already been processed');
    }

    if (invitation.expiresAt < new Date()) {
      // Mark as expired
      await this.invitationRepository.update(invitation.id, { status: 'expired' as any });
      throw new ValidationError('Invitation has expired');
    }

    // Check if OTP has been verified
    if (!invitation.otpVerified) {
      console.log('❌ SetInvitationPasswordUseCase: OTP not verified for invitation:', invitation.id);
      throw new ValidationError('Email verification required. Please verify your email with the OTP sent to you.');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(invitation.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Validate password
    if (password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long');
    }

    // Validate required fields
    if (!name.trim()) {
      throw new ValidationError('Name is required');
    }

    if (!phone.trim()) {
      throw new ValidationError('Phone number is required');
    }

    // Hash password
    const hashedPassword = await this.authService.hashPassword(password);

    // Create user data based on role
    const userData: CreateUserData = {
      email: invitation.email,
      name: name.trim(),
      password: hashedPassword,
      role: invitation.role as any,
      phone: phone.trim(),
      collegeId: invitation.collegeId,
      departmentId: invitation.departmentId || undefined,
    };

    // For non-student roles, we don't need class, semester, rollNumber
    // These fields are only required for students and will be handled in the full registration flow

    // Create user
    const user = await this.userRepository.create(userData);

    // Update user status to active
    await this.userRepository.update(user.id, { status: UserStatus.ACTIVE });

    // Mark invitation as accepted
    console.log('🔄 SetInvitationPasswordUseCase: Updating invitation status to accepted for ID:', invitation.id);
    const updatedInvitation = await this.invitationRepository.update(invitation.id, {
      status: 'accepted' as any,
      acceptedAt: new Date(),
    });

    console.log('✅ SetInvitationPasswordUseCase: Invitation updated:', updatedInvitation ? {
      id: updatedInvitation.id,
      status: updatedInvitation.status,
      acceptedAt: updatedInvitation.acceptedAt
    } : 'null');

    // Verify the update was successful
    if (!updatedInvitation || updatedInvitation.status !== 'accepted') {
      console.log('❌ SetInvitationPasswordUseCase: Failed to update invitation status');
      throw new Error('Failed to update invitation status');
    }

    // Get college and department information for welcome email
    const college = await this.collegeRepository.findById(invitation.collegeId);
    let department = null;
    if (invitation.departmentId) {
      department = await this.departmentRepository.findById(invitation.departmentId);
    }

    // Send welcome email
    await this.emailService.sendWelcome(user.email, {
      name: user.name,
      role: user.role,
      collegeName: college?.name || 'Unknown College',
      departmentName: department?.name,
      loginUrl: `${process.env.FRONTEND_URL}/login`,
    });

    console.log('✅ SetInvitationPasswordUseCase: Welcome email sent to:', user.email);

    // Return user without password
    return {
      ...user,
      password: undefined,
    };
  }
}
