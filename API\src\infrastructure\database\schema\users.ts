import { pgTable, uuid, varchar, text, timestamp, pgEnum, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const userRoleEnum = treedev.enum('user_role', ['admin', 'principal', 'hod', 'staff', 'student']);
export const userStatusEnum = treedev.enum('user_status', ['active', 'inactive', 'pending']);

export const users = treedev.table('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  password: varchar('password', { length: 255 }),
  role: userRoleEnum('role').notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  status: userStatusEnum('status').notNull().default('pending'),
  collegeId: uuid('college_id'),
  departmentId: uuid('department_id'),
  classInCharge: varchar('class_in_charge', { length: 100 }),
  class: varchar('class', { length: 50 }),
  semester: varchar('semester', { length: 20 }),
  rollNumber: varchar('roll_number', { length: 50 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  lastLogin: timestamp('last_login'),
});

export const insertUserSchema = createInsertSchema(users);
export const selectUserSchema = createSelectSchema(users);

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
