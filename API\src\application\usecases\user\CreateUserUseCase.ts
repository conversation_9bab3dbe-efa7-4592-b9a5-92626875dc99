import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { IAuthService } from '../../../domain/services/IAuthService';
import { User, CreateUserData, UserRole } from '../../../domain/entities/User';
import { NotFoundError, ForbiddenError, ConflictError, ValidationError } from '../../../presentation/middleware/errorHandler';

export interface CreateUserRequest {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  userData: CreateUserData;
}

export class CreateUserUseCase {
  constructor(
    private userRepository: IUserRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository,
    private authService: IAuthService
  ) {}

  async execute(request: CreateUserRequest): Promise<User> {
    const { requesterId, requesterRole, requesterCollegeId, requesterDepartmentId, userData } = request;

    // Validate permissions
    this.validateCreatePermissions(requesterRole, requesterCollegeId, requesterDepartmentId, userData);

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Validate college exists if provided
    if (userData.collegeId) {
      const college = await this.collegeRepository.findById(userData.collegeId);
      if (!college) {
        throw new NotFoundError('College not found');
      }
    }

    // Validate department exists if provided
    if (userData.departmentId) {
      const department = await this.departmentRepository.findById(userData.departmentId);
      if (!department) {
        throw new NotFoundError('Department not found');
      }
    }

    // Hash password
    const hashedPassword = await this.authService.hashPassword(userData.password);

    // Create user data with hashed password
    const userDataWithHashedPassword: CreateUserData = {
      ...userData,
      password: hashedPassword,
    };

    // Validate role-specific requirements
    this.validateRoleRequirements(userDataWithHashedPassword);

    // Create user
    const user = await this.userRepository.create(userDataWithHashedPassword);

    // Return user without password
    return {
      ...user,
      password: undefined,
    };
  }

  private validateCreatePermissions(
    requesterRole: UserRole,
    requesterCollegeId: string | undefined,
    requesterDepartmentId: string | undefined,
    userData: CreateUserData
  ): void {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can create any user
        break;
      
      case UserRole.PRINCIPAL:
        // Principal can create users in their college
        if (!requesterCollegeId) {
          throw new ForbiddenError('Principal must be assigned to a college');
        }
        if (userData.collegeId && userData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Principal can only create users in their own college');
        }
        // Principal cannot create admin or other principals
        if (userData.role === UserRole.ADMIN || userData.role === UserRole.PRINCIPAL) {
          throw new ForbiddenError('Principal cannot create admin or principal users');
        }
        break;
      
      case UserRole.HOD:
        // HOD can create staff and students in their department
        if (!requesterCollegeId || !requesterDepartmentId) {
          throw new ForbiddenError('HOD must be assigned to a college and department');
        }
        if (userData.collegeId && userData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('HOD can only create users in their own college');
        }
        if (userData.departmentId && userData.departmentId !== requesterDepartmentId) {
          throw new ForbiddenError('HOD can only create users in their own department');
        }
        // HOD can only create staff and students
        if (![UserRole.STAFF, UserRole.STUDENT].includes(userData.role)) {
          throw new ForbiddenError('HOD can only create staff and student users');
        }
        break;
      
      case UserRole.STAFF:
        // Staff can only create students in their department
        if (!requesterCollegeId || !requesterDepartmentId) {
          throw new ForbiddenError('Staff must be assigned to a college and department');
        }
        if (userData.collegeId && userData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Staff can only create users in their own college');
        }
        if (userData.departmentId && userData.departmentId !== requesterDepartmentId) {
          throw new ForbiddenError('Staff can only create users in their own department');
        }
        // Staff can only create students
        if (userData.role !== UserRole.STUDENT) {
          throw new ForbiddenError('Staff can only create student users');
        }
        break;
      
      default:
        throw new ForbiddenError('Insufficient permissions to create users');
    }
  }

  private validateRoleRequirements(userData: CreateUserData): void {
    switch (userData.role) {
      case UserRole.STUDENT:
        if (!userData.collegeId) {
          throw new ValidationError('Students must be assigned to a college');
        }
        if (!userData.departmentId) {
          throw new ValidationError('Students must be assigned to a department');
        }
        break;
      
      case UserRole.STAFF:
        if (!userData.collegeId) {
          throw new ValidationError('Staff must be assigned to a college');
        }
        if (!userData.departmentId) {
          throw new ValidationError('Staff must be assigned to a department');
        }
        break;
      
      case UserRole.HOD:
        if (!userData.collegeId) {
          throw new ValidationError('HOD must be assigned to a college');
        }
        if (!userData.departmentId) {
          throw new ValidationError('HOD must be assigned to a department');
        }
        break;
      
      case UserRole.PRINCIPAL:
        if (!userData.collegeId) {
          throw new ValidationError('Principal must be assigned to a college');
        }
        break;
      
      case UserRole.ADMIN:
        // Admin doesn't require college/department assignment
        break;
    }
  }
}
