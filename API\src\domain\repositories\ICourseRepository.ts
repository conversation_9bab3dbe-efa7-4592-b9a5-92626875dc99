import { Course, CreateCourseData, UpdateCourseData } from '../entities/Course';

export interface CourseFilters {
  limit?: number;
  offset?: number;
  search?: string;
  status?: string;
  departmentId?: string;
  collegeId?: string;
  semester?: string;
}

export interface ICourseRepository {
  create(courseData: CreateCourseData): Promise<Course>;
  findById(id: string): Promise<Course | null>;
  findByCode(code: string, departmentId: string): Promise<Course | null>;
  findAll(filters?: CourseFilters): Promise<Course[]>;
  update(id: string, courseData: UpdateCourseData): Promise<Course | null>;
  delete(id: string): Promise<boolean>;
  findByDepartment(departmentId: string): Promise<Course[]>;
  findByCollege(collegeId: string): Promise<Course[]>;
  count(): Promise<number>;
  countByDepartment(departmentId: string): Promise<number>;
  countByCollege(collegeId: string): Promise<number>;
}
