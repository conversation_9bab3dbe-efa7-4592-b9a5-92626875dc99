import React, { useState } from 'react';
import { <PERSON>, User, TreeP<PERSON>, CheckCircle, XCircle, Clock } from 'lucide-react';

interface Student {
  id: string;
  name: string;
  email: string;
  class: string;
  semester: string;
  rollNumber: string;
  treesPlanted: number;
  approved: number;
  pending: number;
  rejected: number;
  completionRate: number;
  lastUpload?: string;
}

interface StudentListTableProps {
  students: Student[];
  title: string;
  loading?: boolean;
}

interface StudentDetailsDialogProps {
  student: Student | null;
  isOpen: boolean;
  onClose: () => void;
}

function StudentDetailsDialog({ student, isOpen, onClose }: StudentDetailsDialogProps) {
  if (!isOpen || !student) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">Student Details</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <p className="text-gray-900">{student.name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <p className="text-gray-900">{student.email}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Class</label>
              <p className="text-gray-900">{student.class}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Semester</label>
              <p className="text-gray-900">{student.semester}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Roll Number</label>
              <p className="text-gray-900">{student.rollNumber}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Completion Rate</label>
              <p className="text-gray-900">{student.completionRate.toFixed(1)}%</p>
            </div>
          </div>

          {/* Tree Planting Stats */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Tree Planting Statistics</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TreePine className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-900">Total Trees</span>
                </div>
                <p className="text-2xl font-bold text-green-600">{student.treesPlanted}</p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Approved</span>
                </div>
                <p className="text-2xl font-bold text-blue-600">{student.approved}</p>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-5 w-5 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-900">Pending</span>
                </div>
                <p className="text-2xl font-bold text-yellow-600">{student.pending}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <span className="text-sm font-medium text-red-900">Rejected</span>
                </div>
                <p className="text-2xl font-bold text-red-600">{student.rejected}</p>
              </div>
            </div>
          </div>

          {/* Last Upload */}
          {student.lastUpload && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Last Upload</label>
              <p className="text-gray-900">{new Date(student.lastUpload).toLocaleDateString()}</p>
            </div>
          )}
        </div>

        <div className="flex justify-end mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}

export default function StudentListTable({ students, title, loading = false }: StudentListTableProps) {
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleViewDetails = (student: Student) => {
    setSelectedStudent(student);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedStudent(null);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        
        {students.length === 0 ? (
          <div className="text-center py-8">
            <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No students found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Name</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Class</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Roll No.</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Trees Planted</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Completion Rate</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {students.map((student) => (
                  <tr key={student.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <p className="font-medium text-gray-900">{student.name}</p>
                        <p className="text-sm text-gray-500">{student.email}</p>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-gray-700">{student.class}</td>
                    <td className="py-3 px-4 text-gray-700">{student.rollNumber}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-1">
                        <TreePine className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-gray-900">{student.treesPlanted}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        student.completionRate >= 80 
                          ? 'bg-green-100 text-green-800'
                          : student.completionRate >= 60
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {student.completionRate.toFixed(1)}%
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <button
                        onClick={() => handleViewDetails(student)}
                        className="inline-flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <StudentDetailsDialog
        student={selectedStudent}
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
      />
    </>
  );
}
