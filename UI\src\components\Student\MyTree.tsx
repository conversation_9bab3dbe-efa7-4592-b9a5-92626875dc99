import React, { useState, useRef } from 'react';
import { MapPin, Camera, CheckCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useCreateTreePlanting } from '../../hooks/useTreePlantings';
import { useToast } from '../UI/Toast';
import tree from '../../assets/mytree.png';
import MapPreview from './MapPreview';
import './fix-leaflet-icon';

// Safe toast hook wrapper
const useSafeToast = () => {
  try {
    return useToast();
  } catch (error) {
    console.error('Toast context not available:', error);
    return {
      success: (title: string, msg?: string) => console.log('✅', title, msg),
      error: (title: string, msg?: string) => console.error('❌', title, msg),
      warning: (title: string, msg?: string) => console.warn('⚠️', title, msg),
      info: (title: string, msg?: string) => console.info('ℹ️', title, msg),
    };
  }
};

const MyTree: React.FC = () => {
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const createTreePlanting = useCreateTreePlanting();
  const toast = useSafeToast();

  const [formData, setFormData] = useState({
    plantingDate: '',
    location: '',
    treeType: '',
    description: '',
    semester: '',
    academicYear: new Date().getFullYear().toString(),
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [locationPinned, setLocationPinned] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [mapCoordinates, setMapCoordinates] = useState<[number, number] | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileSelect = (file: File) => {
    if (file && (file.type.startsWith('image/') || file.type.startsWith('video/'))) {
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    } else {
      toast.error('Invalid File', 'Please select an image or video');
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) handleFileSelect(e.target.files[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const handleLocationPin = () => {
    navigator.geolocation.getCurrentPosition(
      (pos) => {
        const { latitude, longitude } = pos.coords;
        const coords: [number, number] = [latitude, longitude];
        setMapCoordinates(coords);
        setFormData((prev) => ({ ...prev, location: coords.join(', ') }));
        setLocationPinned(true);
        toast.success('Location Pinned', 'Your current location was captured.');
      },
      () => {
        toast.error('Location Error', 'Could not get your location.');
      }
    );
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files?.[0]) handleFileSelect(e.dataTransfer.files[0]);
  };

  const handleDragOver = (e: React.DragEvent) => e.preventDefault();
  const handleDragLeave = () => setDragActive(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile || !formData.plantingDate || !formData.location) {
      toast.error('Missing Info', 'Please fill all required fields and upload a file.');
      return;
    }

    setIsUploading(true);
    try {
      const upload = new FormData();
      Object.entries(formData).forEach(([k, v]) => upload.append(k, v));
      upload.append('media', selectedFile);
      await createTreePlanting.execute(upload);
      toast.success('Success', 'Upload completed');

      setFormData({
        plantingDate: '',
        location: '',
        treeType: '',
        description: '',
        semester: '',
        academicYear: new Date().getFullYear().toString(),
      });
      setSelectedFile(null);
      setPreviewUrl('');
      setLocationPinned(false);
      setMapCoordinates(null);
    } catch (err: any) {
      toast.error('Error', err.message || 'Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#fefef9] p-6">
      <div className="w-full max-w-3xl bg-white rounded-2xl shadow p-8 relative overflow-hidden right-16">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Upload Progress</h1>
        <form onSubmit={handleSubmit} className="space-y-6 z-10 relative">
          {/* File Upload */}
          <div
            className={`border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition ${dragActive
              ? 'border-green-400 bg-green-50'
              : selectedFile
                ? 'border-green-300 bg-green-50'
                : 'border-gray-300 hover:border-gray-400'
              }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            {previewUrl && selectedFile ? (
              <div className="space-y-4">
                {selectedFile.type.startsWith('image/') ? (
                  <img src={previewUrl} alt="Preview" className="max-h-48 mx-auto rounded-lg object-cover" />
                ) : (
                  <video src={previewUrl} className="max-h-48 mx-auto rounded-lg" controls />
                )}
                <p className="text-sm text-gray-600">{selectedFile.name}</p>
                <button
                  type="button"
                  onClick={() => {
                    setSelectedFile(null);
                    setPreviewUrl('');
                  }}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove file
                </button>
              </div>
            ) : (
              <>
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                  <Camera className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-lg text-gray-700 mb-1">Drag and drop photo here, or</p>
                <button
                  type="button"
                  onClick={handleBrowseClick}
                  className="text-green-600 hover:text-green-700 font-medium"
                >
                  Browse
                </button>
              </>
            )}
            <input ref={fileInputRef} type="file" accept="image/*,video/*" onChange={handleFileInputChange} className="hidden" />
          </div>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-1 block">(Optional Caption)</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Write a caption..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500"
              />
            </div>

            <div className="w-full md:w-1/3">
              <label className="text-sm font-medium text-gray-700 mb-1 block">Date</label>
              <input
                type="date"
                name="plantingDate"
                value={formData.plantingDate}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500"
                required
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row md:items-start justify-between gap-8">
            {/* Location Label & Button */}
            <div className="flex-1 space-y-3">
              <div className="flex items-center space-x-2">
                <CheckCircle className={`w-5 h-5 ${locationPinned ? 'text-green-500' : 'text-gray-400'}`} />
                <span className={`text-sm font-medium ${locationPinned ? 'text-green-700' : 'text-gray-600'}`}>
                  Location Pinned
                </span>
              </div>

              <div className="pt-2">
                <button
                  type="button"
                  onClick={handleLocationPin}
                  className="inline-flex items-center space-x-2 text-sm text-green-700 bg-green-100 px-4 py-2 rounded-lg hover:bg-green-200"
                >
                  <MapPin className="w-4 h-4" />
                  <span>Pin Location</span>
                </button>
              </div>
            </div>

            {/* Map Preview */}
            <div className="w-full md:w-60 h-36 md:mt-0 mb-6">
              {mapCoordinates && <MapPreview coordinates={mapCoordinates} />}
            </div>
          </div>

          <button
            type="submit"
            disabled={isUploading}
            className="w-full bg-green-600 text-white py-4 px-6 rounded-lg font-semibold hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {isUploading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Uploading...</span>
              </div>
            ) : (
              'Upload Progress Photo'
            )}
          </button>
        </form>
      </div>
      <img
        src={tree}
        alt="Tree Illustration"
        className="fixed z-10 pointer-events-none select-none"
      />
    </div>
  );
};

export default MyTree;