import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { CreateDepartmentData, Department } from '../../../domain/entities/Department';
import { UserRole } from '../../../domain/entities/User';
import { ValidationError, ForbiddenError, ConflictError, NotFoundError } from '../../../presentation/middleware/errorHandler';

export interface CreateDepartmentRequest extends CreateDepartmentData {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
}

export class CreateDepartmentUseCase {
  constructor(
    private departmentRepository: IDepartmentRepository,
    private collegeRepository: ICollegeRepository,
    private userRepository: IUserRepository
  ) {}

  async execute(request: CreateDepartmentRequest): Promise<Department> {
    const { requesterId, requesterRole, requesterCollegeId, ...departmentData } = request;

    // Validate permissions
    this.validatePermissions(requesterRole, requesterCollegeId, departmentData.collegeId);

    // Validate college exists
    const college = await this.collegeRepository.findById(departmentData.collegeId);
    if (!college) {
      throw new NotFoundError('College not found');
    }

    // Check if department with same code already exists in this college
    const existingDepartment = await this.departmentRepository.findByCode(
      departmentData.code,
      departmentData.collegeId
    );
    if (existingDepartment) {
      throw new ConflictError('Department with this code already exists in the college');
    }

    // Validate HOD if provided
    if (departmentData.hodId) {
      const hod = await this.userRepository.findById(departmentData.hodId);
      if (!hod) {
        throw new ValidationError('HOD not found');
      }

      if (hod.role !== UserRole.HOD) {
        throw new ValidationError('User is not an HOD');
      }

      if (hod.collegeId !== departmentData.collegeId) {
        throw new ValidationError('HOD must belong to the same college');
      }

      // Check if HOD is already assigned to another department
      const existingDepartmentWithHOD = await this.departmentRepository.findByHOD(departmentData.hodId);
      if (existingDepartmentWithHOD) {
        throw new ConflictError('HOD is already assigned to another department');
      }
    }

    // Create department
    const department = await this.departmentRepository.create(departmentData);

    // Update HOD's department assignment if provided
    if (departmentData.hodId) {
      await this.userRepository.update(departmentData.hodId, {
        departmentId: department.id,
      });
    }

    return department;
  }

  private validatePermissions(
    requesterRole: UserRole,
    requesterCollegeId?: string,
    targetCollegeId?: string
  ): void {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can create departments in any college
        break;

      case UserRole.PRINCIPAL:
        // Principal can only create departments in their own college
        if (!requesterCollegeId || requesterCollegeId !== targetCollegeId) {
          throw new ForbiddenError('Principal can only create departments in their own college');
        }
        break;

      case UserRole.HOD:
        // HOD can create departments in their own college
        if (!requesterCollegeId || requesterCollegeId !== targetCollegeId) {
          throw new ForbiddenError('HOD can only create departments in their own college');
        }
        break;

      default:
        throw new ForbiddenError('Only admin, principal, and HOD can create departments');
    }
  }
}
