import React, { useState, useEffect } from 'react';
import { Users, Building, GraduationCap, UserPlus, TrendingUp, Calendar } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { User, Department, College } from '../../types';

interface PrincipalDashboardProps {
  onNavigate?: (tab: string) => void;
}

const PrincipalDashboard: React.FC<PrincipalDashboardProps> = ({ onNavigate }) => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalStaff: 0,
    totalStudents: 0,
    totalDepartments: 0,
    pendingRequests: 0
  });
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [usersRes, departmentsRes, collegesRes] = await Promise.all([
        fetch('/src/data/users.json'),
        fetch('/src/data/departments.json'),
        fetch('/src/data/colleges.json')
      ]);
      
      const users: User[] = await usersRes.json();
      const departments: Department[] = await departmentsRes.json();
      const colleges: College[] = await collegesRes.json();

      const college = colleges.find(c => c.id === user?.collegeId);
      const collegeUsers = users.filter(u => u.collegeId === user?.collegeId);
      const collegeDepartments = departments.filter(d => d.collegeId === user?.collegeId);

      setStats({
        totalStaff: collegeUsers.filter(u => u.role === 'staff' || u.role === 'hod').length,
        totalStudents: collegeUsers.filter(u => u.role === 'student').length,
        totalDepartments: collegeDepartments.length,
        pendingRequests: 0
      });

      // Create recent activity
      const activity = collegeUsers
        .filter(u => u.createdAt)
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)
        .map(u => ({
          type: 'user_joined',
          message: `${u.name} joined as ${u.role}`,
          timestamp: u.createdAt,
          status: u.status
        }));

      setRecentActivity(activity);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const statCards = [
    {
      title: 'Total Staff',
      value: stats.totalStaff,
      icon: Users,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Total Students',
      value: stats.totalStudents,
      icon: GraduationCap,
      color: 'bg-green-500',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Departments',
      value: stats.totalDepartments,
      icon: Building,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Pending Requests',
      value: stats.pendingRequests,
      icon: UserPlus,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Principal Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's your college overview.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className={`${stat.bgColor} p-6 rounded-xl border border-gray-100`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-xl border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            <TrendingUp className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.status === 'active' ? 'bg-green-500' : 'bg-orange-500'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(activity.timestamp).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white p-6 rounded-xl border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
            <Calendar className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-3">
            <button
              onClick={() => onNavigate?.('invitations')}
              className="w-full p-4 text-left bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <Users className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">Invite Staff</p>
                  <p className="text-sm text-green-600">Send invitations to new staff members</p>
                </div>
              </div>
            </button>
            
            <button className="w-full p-4 text-left bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
              <div className="flex items-center space-x-3">
                <Building className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">Manage Departments</p>
                  <p className="text-sm text-blue-600">View and manage college departments</p>
                </div>
              </div>
            </button>
            
            <button className="w-full p-4 text-left bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
              <div className="flex items-center space-x-3">
                <GraduationCap className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="font-medium text-purple-900">View Students</p>
                  <p className="text-sm text-purple-600">Monitor student registrations</p>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrincipalDashboard;