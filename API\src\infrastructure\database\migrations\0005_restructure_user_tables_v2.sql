-- Create new role-specific tables
CREATE TABLE IF NOT EXISTS "treedev"."admin_users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"email_id" varchar(255) NOT NULL,
	"phone_number" varchar(20) NOT NULL,
	"last_seen" timestamp,
	"created_on" timestamp DEFAULT now() NOT NULL,
	"modified_on" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	"modified_by" uuid,
	CONSTRAINT "admin_users_email_id_unique" UNIQUE("email_id")
);

CREATE TABLE IF NOT EXISTS "treedev"."principals" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"email_id" varchar(255) NOT NULL,
	"phone_number" varchar(20) NOT NULL,
	"college_id" uuid NOT NULL,
	"last_seen" timestamp,
	"created_on" timestamp DEFAULT now() NOT NULL,
	"modified_on" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	"modified_by" uuid,
	CONSTRAINT "principals_email_id_unique" UNIQUE("email_id")
);

CREATE TABLE IF NOT EXISTS "treedev"."hods" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"email_id" varchar(255) NOT NULL,
	"phone_number" varchar(20) NOT NULL,
	"college_id" uuid NOT NULL,
	"department_id" uuid NOT NULL,
	"last_seen" timestamp,
	"created_on" timestamp DEFAULT now() NOT NULL,
	"modified_on" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	"modified_by" uuid,
	CONSTRAINT "hods_email_id_unique" UNIQUE("email_id")
);

CREATE TABLE IF NOT EXISTS "treedev"."staffs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"email_id" varchar(255) NOT NULL,
	"phone_number" varchar(20) NOT NULL,
	"college_id" uuid NOT NULL,
	"department_id" uuid NOT NULL,
	"last_seen" timestamp,
	"created_on" timestamp DEFAULT now() NOT NULL,
	"modified_on" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	"modified_by" uuid,
	CONSTRAINT "staffs_email_id_unique" UNIQUE("email_id")
);

CREATE TABLE IF NOT EXISTS "treedev"."students" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"email_id" varchar(255) NOT NULL,
	"phone_number" varchar(20) NOT NULL,
	"college_id" uuid NOT NULL,
	"department_id" uuid NOT NULL,
	"class" varchar(50),
	"semester" varchar(20),
	"roll_number" varchar(50),
	"last_seen" timestamp,
	"created_on" timestamp DEFAULT now() NOT NULL,
	"modified_on" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	"modified_by" uuid,
	CONSTRAINT "students_email_id_unique" UNIQUE("email_id")
);

-- Add last_seen column to users table if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'last_seen') THEN
        ALTER TABLE "treedev"."users" ADD COLUMN "last_seen" timestamp;
    END IF;
END $$;

-- Migrate existing user data to role-specific tables
-- Migrate admin users
INSERT INTO "treedev"."admin_users" (name, email_id, phone_number, last_seen, created_by)
SELECT 
    COALESCE(name, 'Admin User') as name,
    email as email_id,
    COALESCE(phone, '') as phone_number,
    COALESCE(last_login, now()) as last_seen,
    id as created_by
FROM "treedev"."users" 
WHERE role = 'admin'
ON CONFLICT (email_id) DO NOTHING;

-- Migrate principal users
INSERT INTO "treedev"."principals" (name, email_id, phone_number, college_id, last_seen, created_by)
SELECT 
    COALESCE(name, 'Principal') as name,
    email as email_id,
    COALESCE(phone, '') as phone_number,
    college_id,
    COALESCE(last_login, now()) as last_seen,
    id as created_by
FROM "treedev"."users" 
WHERE role = 'principal' AND college_id IS NOT NULL
ON CONFLICT (email_id) DO NOTHING;

-- Migrate HOD users
INSERT INTO "treedev"."hods" (name, email_id, phone_number, college_id, department_id, last_seen, created_by)
SELECT 
    COALESCE(name, 'HOD') as name,
    email as email_id,
    COALESCE(phone, '') as phone_number,
    college_id,
    department_id,
    COALESCE(last_login, now()) as last_seen,
    id as created_by
FROM "treedev"."users" 
WHERE role = 'hod' AND college_id IS NOT NULL AND department_id IS NOT NULL
ON CONFLICT (email_id) DO NOTHING;

-- Migrate staff users
INSERT INTO "treedev"."staffs" (name, email_id, phone_number, college_id, department_id, last_seen, created_by)
SELECT 
    COALESCE(name, 'Staff') as name,
    email as email_id,
    COALESCE(phone, '') as phone_number,
    college_id,
    department_id,
    COALESCE(last_login, now()) as last_seen,
    id as created_by
FROM "treedev"."users" 
WHERE role = 'staff' AND college_id IS NOT NULL AND department_id IS NOT NULL
ON CONFLICT (email_id) DO NOTHING;

-- Migrate student users
INSERT INTO "treedev"."students" (name, email_id, phone_number, college_id, department_id, class, semester, roll_number, last_seen, created_by)
SELECT 
    COALESCE(name, 'Student') as name,
    email as email_id,
    COALESCE(phone, '') as phone_number,
    college_id,
    department_id,
    class,
    semester,
    roll_number,
    COALESCE(last_login, now()) as last_seen,
    id as created_by
FROM "treedev"."users" 
WHERE role = 'student' AND college_id IS NOT NULL AND department_id IS NOT NULL
ON CONFLICT (email_id) DO NOTHING;

-- Update last_seen from last_login for existing users
UPDATE "treedev"."users" SET last_seen = COALESCE(last_login, now()) WHERE last_seen IS NULL;

-- Drop columns that are no longer needed (only if they exist)
DO $$ 
BEGIN 
    -- Drop name column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'name') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "name";
    END IF;
    
    -- Drop phone column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'phone') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "phone";
    END IF;
    
    -- Drop college_id column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'college_id') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "college_id";
    END IF;
    
    -- Drop department_id column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'department_id') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "department_id";
    END IF;
    
    -- Drop class_in_charge column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'class_in_charge') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "class_in_charge";
    END IF;
    
    -- Drop class column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'class') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "class";
    END IF;
    
    -- Drop semester column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'semester') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "semester";
    END IF;
    
    -- Drop roll_number column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'roll_number') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "roll_number";
    END IF;
    
    -- Drop last_login column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'treedev' AND table_name = 'users' AND column_name = 'last_login') THEN
        ALTER TABLE "treedev"."users" DROP COLUMN "last_login";
    END IF;
END $$;
