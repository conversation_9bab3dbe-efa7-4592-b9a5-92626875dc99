export interface User {
  id: string;
  email: string;
  name: string;
  password?: string;
  role: UserRole;
  phone: string;
  status: UserStatus;
  collegeId: string | null;
  departmentId: string | null;
  classInCharge?: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  PRINCIPAL = 'principal',
  HOD = 'hod',
  STAFF = 'staff',
  STUDENT = 'student'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending'
}

export interface CreateUserData {
  email: string;
  name: string;
  password: string;
  role: UserRole;
  phone: string;
  collegeId?: string;
  departmentId?: string;
  classInCharge?: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
}

export interface UpdateUserData {
  name?: string;
  phone?: string;
  password?: string;
  status?: UserStatus;
  collegeId?: string;
  departmentId?: string;
  classInCharge?: string;
  class?: string;
  semester?: string;
  lastLogin?: Date;
}
