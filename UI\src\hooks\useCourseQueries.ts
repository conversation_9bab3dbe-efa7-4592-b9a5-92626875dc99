import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { coursesApi } from '../services/api';
import { Course } from '../types';

// Query Keys
export const courseKeys = {
  all: ['courses'] as const,
  lists: () => [...courseKeys.all, 'list'] as const,
  list: (filters: Record<string, string> = {}) => [...courseKeys.lists(), filters] as const,
  details: () => [...courseKeys.all, 'detail'] as const,
  detail: (id: string) => [...courseKeys.details(), id] as const,
  stats: (id: string) => [...courseKeys.all, 'stats', id] as const,
};

// Queries
export function useCourses(params?: Record<string, string>) {
  return useQuery({
    queryKey: courseKeys.list(params || {}),
    queryFn: () => coursesApi.getCourses(params),
  });
}

export function useCourse(id: string) {
  return useQuery({
    queryKey: courseKeys.detail(id),
    queryFn: () => coursesApi.getCourseById(id),
    select: (response) => response.data,
    enabled: !!id,
  });
}

export function useCourseStats(id: string) {
  return useQuery({
    queryKey: courseKeys.stats(id),
    queryFn: () => coursesApi.getCourseStats(id),
    select: (response) => response.data,
    enabled: !!id,
  });
}

// Mutations
export function useCreateCourse() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<Course, 'id' | 'createdAt' | 'updatedAt'>) => 
      coursesApi.createCourse(data),
    onSuccess: (newCourse) => {
      // Invalidate and refetch courses list
      queryClient.invalidateQueries({ queryKey: courseKeys.lists() });

      // Optionally add the new course to the cache
      queryClient.setQueryData(courseKeys.detail(newCourse.id), newCourse);
    },
    onError: (error) => {
      console.error('Failed to create course:', error);
    },
  });
}

export function useUpdateCourse() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Course> }) =>
      coursesApi.updateCourse(id, data),
    onSuccess: (updatedCourse, variables) => {
      // Invalidate and refetch courses list
      queryClient.invalidateQueries({ queryKey: courseKeys.lists() });
      
      // Update the specific course in cache
      queryClient.setQueryData(courseKeys.detail(variables.id), updatedCourse);
    },
    onError: (error) => {
      console.error('Failed to update course:', error);
    },
  });
}

export function useDeleteCourse() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => coursesApi.deleteCourse(id),
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch courses list
      queryClient.invalidateQueries({ queryKey: courseKeys.lists() });
      
      // Remove the deleted course from cache
      queryClient.removeQueries({ queryKey: courseKeys.detail(deletedId) });
    },
    onError: (error) => {
      console.error('Failed to delete course:', error);
    },
  });
}
