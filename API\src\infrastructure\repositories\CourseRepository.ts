import { eq, and, ilike, count, desc } from 'drizzle-orm';
import { db } from '../database/connection';
import { courses } from '../database/schema/courses';
import { ICourseRepository, CourseFilters } from '../../domain/repositories/ICourseRepository';
import { Course, CreateCourseData, UpdateCourseData } from '../../domain/entities/Course';

export class CourseRepository implements ICourseRepository {
  async create(courseData: CreateCourseData): Promise<Course> {
    const [course] = await db
      .insert(courses)
      .values({
        ...courseData,
        status: 'active'
      })
      .returning();

    return this.mapToDomain(course);
  }

  async findById(id: string): Promise<Course | null> {
    const [course] = await db
      .select()
      .from(courses)
      .where(eq(courses.id, id));

    return course ? this.mapToDomain(course) : null;
  }

  async findByCode(code: string, departmentId: string): Promise<Course | null> {
    const [course] = await db
      .select()
      .from(courses)
      .where(and(
        eq(courses.code, code),
        eq(courses.departmentId, departmentId)
      ));

    return course ? this.mapToDomain(course) : null;
  }

  async findAll(filters: CourseFilters = {}): Promise<Course[]> {
    let query = db.select().from(courses);

    const conditions = [];

    if (filters.search) {
      conditions.push(
        ilike(courses.name, `%${filters.search}%`)
      );
    }

    if (filters.status) {
      conditions.push(eq(courses.status, filters.status as any));
    }

    if (filters.departmentId) {
      conditions.push(eq(courses.departmentId, filters.departmentId));
    }

    if (filters.collegeId) {
      conditions.push(eq(courses.collegeId, filters.collegeId));
    }

    if (filters.semester) {
      conditions.push(eq(courses.semester, filters.semester));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    query = query.orderBy(desc(courses.createdAt));

    if (filters.limit) {
      query = query.limit(filters.limit);
    }

    if (filters.offset) {
      query = query.offset(filters.offset);
    }

    const results = await query;
    return results.map(course => this.mapToDomain(course));
  }

  async update(id: string, courseData: UpdateCourseData): Promise<Course | null> {
    const [course] = await db
      .update(courses)
      .set({
        ...courseData,
        updatedAt: new Date()
      })
      .where(eq(courses.id, id))
      .returning();

    return course ? this.mapToDomain(course) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db
      .delete(courses)
      .where(eq(courses.id, id));

    return result.rowCount > 0;
  }

  async findByDepartment(departmentId: string): Promise<Course[]> {
    const results = await db
      .select()
      .from(courses)
      .where(eq(courses.departmentId, departmentId))
      .orderBy(desc(courses.createdAt));

    return results.map(course => this.mapToDomain(course));
  }

  async findByCollege(collegeId: string): Promise<Course[]> {
    const results = await db
      .select()
      .from(courses)
      .where(eq(courses.collegeId, collegeId))
      .orderBy(desc(courses.createdAt));

    return results.map(course => this.mapToDomain(course));
  }

  async count(): Promise<number> {
    const [result] = await db
      .select({ count: count() })
      .from(courses);

    return result.count;
  }

  async countByDepartment(departmentId: string): Promise<number> {
    const [result] = await db
      .select({ count: count() })
      .from(courses)
      .where(eq(courses.departmentId, departmentId));

    return result.count;
  }

  async countByCollege(collegeId: string): Promise<number> {
    const [result] = await db
      .select({ count: count() })
      .from(courses)
      .where(eq(courses.collegeId, collegeId));

    return result.count;
  }

  private mapToDomain(course: any): Course {
    return {
      id: course.id,
      name: course.name,
      code: course.code,
      departmentId: course.departmentId,
      collegeId: course.collegeId,
      credits: course.credits,
      semester: course.semester,
      description: course.description || '',
      status: course.status,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
    };
  }
}
