import { Router } from 'express';
import { CollegeController } from '../controllers/CollegeController';
import { authenticate, authorize } from '../middleware/auth';
import { validate, collegeSchema, updateCollegeSchema, uuidParamSchema, paginationSchema } from '../middleware/validation';
import { UserRole } from '../../domain/entities/User';

const router = Router();
const collegeController = new CollegeController();

// All routes require authentication
router.use(authenticate);

// Create college (admin only)
router.post(
  '/',
  authorize([UserRole.ADMIN]),
  validate(collegeSchema),
  collegeController.createCollege
);

// Get colleges
router.get(
  '/',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD]),
  validate(paginationSchema),
  collegeController.getColleges
);

// Get college by ID
router.get(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL]),
  validate(uuidParamSchema),
  collegeController.getCollegeById
);

// Get college statistics
router.get(
  '/:id/stats',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL]),
  validate(uuidParamSchema),
  collegeController.getCollegeStats
);

// Update college
router.put(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL]),
  validate(uuidParamSchema),
  validate(updateCollegeSchema),
  collegeController.updateCollege
);

// Delete college (admin only)
router.delete(
  '/:id',
  authorize([UserRole.ADMIN]),
  validate(uuidParamSchema),
  collegeController.deleteCollege
);

export default router;
