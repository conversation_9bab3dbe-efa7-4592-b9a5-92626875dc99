import { IStudentRepository } from '../../../domain/repositories/IStudentRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { Student, CreateStudentData } from '../../../domain/entities/Student';
import { UserRole } from '../../../domain/entities/User';
import { NotFoundError, ForbiddenError, ConflictError, ValidationError } from '../../../presentation/middleware/errorHandler';

export interface CreateStudentProfileRequest {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  studentData: {
    name: string;
    email: string;
    phoneNumber: string;
    collegeId?: string;
    departmentId?: string;
    class?: string;
    semester?: string;
    rollNumber?: string;
  };
}

export class CreateStudentProfileUseCase {
  constructor(
    private studentRepository: IStudentRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository
  ) {}

  async execute(request: CreateStudentProfileRequest): Promise<Student> {
    const { requesterId, requesterRole, requesterCollegeId, requesterDepartmentId, studentData } = request;

    // Validate permissions
    this.validateCreatePermissions(requesterRole, requesterCollegeId, requesterDepartmentId, studentData);

    // Check if student profile already exists
    const existingStudent = await this.studentRepository.findByEmail(studentData.email);
    if (existingStudent) {
      throw new ConflictError('Student profile with this email already exists');
    }

    // Validate college exists
    const collegeId = studentData.collegeId || requesterCollegeId;
    if (!collegeId) {
      throw new ValidationError('College ID is required');
    }

    const college = await this.collegeRepository.findById(collegeId);
    if (!college) {
      throw new NotFoundError('College not found');
    }

    // Validate department exists and belongs to college
    const departmentId = studentData.departmentId || requesterDepartmentId;
    if (!departmentId) {
      throw new ValidationError('Department ID is required');
    }

    const department = await this.departmentRepository.findById(departmentId);
    if (!department || department.collegeId !== collegeId) {
      throw new NotFoundError('Department not found or does not belong to the specified college');
    }

    // Create student profile record
    const studentProfileData: CreateStudentData = {
      name: studentData.name,
      emailId: studentData.email,
      phoneNumber: studentData.phoneNumber,
      collegeId,
      departmentId,
      class: studentData.class,
      semester: studentData.semester,
      rollNumber: studentData.rollNumber,
      createdBy: requesterId,
    };

    const student = await this.studentRepository.create(studentProfileData);

    return student;
  }

  private validateCreatePermissions(
    requesterRole: UserRole,
    requesterCollegeId: string | undefined,
    requesterDepartmentId: string | undefined,
    studentData: CreateStudentProfileRequest['studentData']
  ): void {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can create student profiles anywhere
        break;

      case UserRole.PRINCIPAL:
        // Principal can create student profiles in their college
        if (!requesterCollegeId) {
          throw new ForbiddenError('Principal must be assigned to a college');
        }
        if (studentData.collegeId && studentData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Principal can only create student profiles in their own college');
        }
        break;

      case UserRole.HOD:
        // HOD can create student profiles in their college, any department
        if (!requesterCollegeId) {
          throw new ForbiddenError('HOD must be assigned to a college');
        }
        if (studentData.collegeId && studentData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('HOD can only create student profiles in their own college');
        }
        break;

      case UserRole.STAFF:
        // Staff can only create student profiles in their own department
        if (!requesterCollegeId || !requesterDepartmentId) {
          throw new ForbiddenError('Staff must be assigned to a college and department');
        }
        if (studentData.collegeId && studentData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Staff can only create student profiles in their own college');
        }
        if (studentData.departmentId && studentData.departmentId !== requesterDepartmentId) {
          throw new ForbiddenError('Staff can only create student profiles in their own department');
        }
        break;

      default:
        throw new ForbiddenError('Invalid role for creating student profiles');
    }
  }
}
