import React from 'react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Toolt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  Legend
} from 'recharts';

interface BarChartProps {
  data: any[];
  bars: {
    dataKey: string;
    name: string;
    color: string;
  }[];
  title?: string;
  height?: number;
}

const BarChart: React.FC<BarChartProps> = ({ data, bars, title, height = 300 }) => {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm">
      {title && <h3 className="text-xl font-semibold mb-4 text-gray-800">{title}</h3>}
      <ResponsiveContainer width="100%" height={height}>
        <ReBarChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          barCategoryGap={32}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#e5e7eb" />
          <XAxis dataKey="name" tick={{ fill: '#6b7280', fontSize: 12 }} />
          <YAxis tick={{ fill: '#6b7280', fontSize: 12 }} />
          <Tooltip
            contentStyle={{ borderRadius: '8px', fontSize: '14px' }}
            labelStyle={{ fontWeight: 500 }}
          />
          <Legend
            iconType="square"
            wrapperStyle={{
              paddingBottom: 20,
              fontSize: 14,
              color: '#374151'
            }}
          />
          {bars.map((bar, index) => (
            <Bar
              key={index}
              dataKey={bar.dataKey}
              name={bar.name}
              fill={bar.color}
              radius={[10, 10, 0, 0]}
              barSize={40}
            />
          ))}
        </ReBarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BarChart;