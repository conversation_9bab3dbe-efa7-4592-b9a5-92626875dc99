import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { collegesApi } from '../services/api';
import { College } from '../types';

// Query Keys
export const collegeKeys = {
  all: ['colleges'] as const,
  lists: () => [...collegeKeys.all, 'list'] as const,
  list: (filters: Record<string, string> = {}) => [...collegeKeys.lists(), filters] as const,
  details: () => [...collegeKeys.all, 'detail'] as const,
  detail: (id: string) => [...collegeKeys.details(), id] as const,
  stats: (id: string) => [...collegeKeys.all, 'stats', id] as const,
};

// Queries
export function useColleges(params?: Record<string, string>) {
  return useQuery({
    queryKey: collegeKeys.list(params),
    queryFn: () => collegesApi.getColleges(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCollege(id: string) {
  return useQuery({
    queryKey: collegeKeys.detail(id),
    queryFn: () => collegesApi.getCollegeById(id),
    enabled: !!id,
  });
}

export function useCollegeStats(id: string) {
  return useQuery({
    queryKey: collegeKeys.stats(id),
    queryFn: () => collegesApi.getCollegeStats(id),
    enabled: !!id,
  });
}

export function useCollegeComparison() {
  return useQuery({
    queryKey: [...collegeKeys.all, 'comparison'],
    queryFn: () => collegesApi.getCollegeComparison(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Principal-specific hooks
export function useCollegeDepartmentComparison(collegeId?: string) {
  return useQuery({
    queryKey: [...collegeKeys.detail(collegeId || ''), 'department-comparison'],
    queryFn: () => collegesApi.getCollegeDepartmentComparison(collegeId!),
    enabled: !!collegeId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCollegeAllStudents(collegeId?: string) {
  return useQuery({
    queryKey: [...collegeKeys.detail(collegeId || ''), 'all-students'],
    queryFn: () => collegesApi.getCollegeAllStudents(collegeId!),
    enabled: !!collegeId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Transform user data to student format for the table
      return data.map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        class: user.class || 'N/A',
        semester: user.semester || 'N/A',
        rollNumber: user.rollNumber || 'N/A',
        department: user.department?.name || 'N/A',
        treesPlanted: user.treePlantingStats?.totalTrees || 0,
        approved: user.treePlantingStats?.approved || 0,
        pending: user.treePlantingStats?.pending || 0,
        rejected: user.treePlantingStats?.rejected || 0,
        completionRate: user.treePlantingStats?.totalTrees > 0
          ? Math.round((user.treePlantingStats.approved / user.treePlantingStats.totalTrees) * 100)
          : 0,
        lastUpload: user.treePlantingStats?.lastUpload || null,
      }));
    },
  });
}

// Mutations
export function useCreateCollege() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<College, 'id' | 'createdAt' | 'updatedAt'>) => 
      collegesApi.createCollege(data),
    onSuccess: (newCollege) => {
      // Invalidate and refetch colleges list
      queryClient.invalidateQueries({ queryKey: collegeKeys.lists() });
      
      // Optionally add the new college to the cache
      queryClient.setQueryData(collegeKeys.detail(newCollege.id), newCollege);
    },
    onError: (error) => {
      console.error('Failed to create college:', error);
    },
  });
}

export function useUpdateCollege() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<College> }) => 
      collegesApi.updateCollege(id, data),
    onSuccess: (updatedCollege, { id }) => {
      // Update the specific college in cache
      queryClient.setQueryData(collegeKeys.detail(id), updatedCollege);
      
      // Invalidate colleges list to ensure consistency
      queryClient.invalidateQueries({ queryKey: collegeKeys.lists() });
    },
    onError: (error) => {
      console.error('Failed to update college:', error);
    },
  });
}

export function useDeleteCollege() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => collegesApi.deleteCollege(id),
    onMutate: async (deletedId) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: collegeKeys.lists() });
      await queryClient.cancelQueries({ queryKey: collegeKeys.detail(deletedId) });

      // Snapshot the previous value
      const previousColleges = queryClient.getQueryData(collegeKeys.lists());

      // Optimistically update to the new value
      queryClient.setQueriesData(
        { queryKey: collegeKeys.lists() },
        (oldData: College[] | undefined) => {
          if (!oldData) return oldData;
          return oldData.filter(college => college.id !== deletedId);
        }
      );

      // Remove the specific college from cache immediately
      queryClient.removeQueries({ queryKey: collegeKeys.detail(deletedId) });

      // Return a context object with the snapshotted value
      return { previousColleges };
    },
    onError: (err: any, deletedId, context) => {
      // Check if it's a "not found" error - this might mean the college was already deleted
      const isNotFoundError = err.response?.status === 404 ||
                              err.response?.data?.error === 'College not found' ||
                              err.message?.includes('not found');

      if (isNotFoundError) {
        // If it's a "not found" error, don't roll back - the college is actually deleted
        console.log('College was already deleted or not found, keeping optimistic update');
        return;
      }

      // For other errors, roll back the optimistic update
      if (context?.previousColleges) {
        queryClient.setQueryData(collegeKeys.lists(), context.previousColleges);
      }
      console.error('Failed to delete college:', err);
    },
    onSettled: (_, __, deletedId) => {
      // Always refetch after error or success to ensure consistency
      queryClient.invalidateQueries({ queryKey: collegeKeys.lists() });
      // Don't refetch the deleted college detail as it should be gone
    },
  });
}
