export interface Student {
  id: string;
  name: string;
  emailId: string;
  phoneNumber: string;
  collegeId: string;
  departmentId: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
  lastSeen?: Date;
  createdOn: Date;
  modifiedOn: Date;
  createdBy?: string;
  modifiedBy?: string;
}

export interface CreateStudentData {
  name: string;
  emailId: string;
  phoneNumber: string;
  collegeId: string;
  departmentId: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
  createdBy?: string;
}

export interface UpdateStudentData {
  name?: string;
  phoneNumber?: string;
  collegeId?: string;
  departmentId?: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
  lastSeen?: Date;
  modifiedBy?: string;
}
