import { db } from '../connection';

async function addPasswordResetsTable() {
  try {
    console.log('Creating password_reset_status enum...');
    await db.execute(`
      DO $$ BEGIN
        CREATE TYPE password_reset_status AS ENUM('pending', 'used', 'expired');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    console.log('Creating password_resets table...');
    await db.execute(`
      CREATE TABLE IF NOT EXISTS treedev.password_resets (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id uuid NOT NULL,
        email varchar(255) NOT NULL,
        token varchar(255) NOT NULL UNIQUE,
        otp varchar(6) NOT NULL,
        status password_reset_status DEFAULT 'pending' NOT NULL,
        expires_at timestamp NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL
      );
    `);

    console.log('Password resets table created successfully!');
  } catch (error) {
    console.error('Error creating password resets table:', error);
    throw error;
  }
}

// Run the migration
addPasswordResetsTable()
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
