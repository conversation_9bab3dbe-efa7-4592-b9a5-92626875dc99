import React, { useState } from 'react';
import { Mail, Send, Clock, CheckCircle, XCircle, User, RefreshCw, GraduationCap } from 'lucide-react';
import { Invitation, User as UserType } from '../../types';
import { useToast } from '../UI/Toast';
import ConfirmDialog from '../UI/ConfirmDialog';
import { useAuth } from '../../hooks/useAuth';
import {
  useRoleBasedInvitations,
  useSendInvitation,
  useCancelInvitation,
  useResendInvitation
} from '../../hooks/useInvitationQueries';
import { useStudentProfiles } from '../../hooks/useStudentProfileQueries';

const StaffInvitationManagement: React.FC = () => {
  const { user } = useAuth();
  const toast = useToast();
  
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [invitationToCancel, setInvitationToCancel] = useState<Invitation | null>(null);
  const [sendingInvitations, setSendingInvitations] = useState<Set<string>>(new Set());

  // Fetch data
  const { data: invitations = [], isLoading: invitationsLoading, refetch: refetchInvitations } = useRoleBasedInvitations(
    user?.role || 'staff',
    user?.collegeId || undefined,
    user?.departmentId || undefined
  );

  // Fetch student profiles created by the current staff member
  const { data: allStudentProfiles = [], isLoading: studentsLoading } = useStudentProfiles({
    departmentId: user?.departmentId,
    collegeId: user?.collegeId
  });

  // Filter student profiles to show only those created by the current staff member
  const students = allStudentProfiles.filter(student => student.createdBy === user?.id);

  // Debug information (can be removed in production)
  console.log('StaffInvitationManagement Debug:', {
    currentUserId: user?.id,
    totalStudentProfilesInDepartment: allStudentProfiles.length,
    studentProfilesCreatedByCurrentStaff: students.length,
    allStudentProfiles: allStudentProfiles.map(s => ({ id: s.id, name: s.name, createdBy: s.createdBy })),
    filteredStudentProfiles: students.map(s => ({ id: s.id, name: s.name, createdBy: s.createdBy }))
  });

  // Mutations
  const sendInvitationMutation = useSendInvitation();
  const cancelInvitationMutation = useCancelInvitation();
  const resendInvitationMutation = useResendInvitation();

  // Handle sending invitation to a specific student
  const handleSendInvitation = async (student: any) => {
    if (!user?.collegeId || !user?.departmentId) {
      toast.error('Error', 'Unable to determine your college or department');
      return;
    }

    setSendingInvitations(prev => new Set(prev).add(student.id));

    try {
      await sendInvitationMutation.mutateAsync({
        email: student.emailId || student.email, // Handle both emailId (student profile) and email (user) fields
        role: 'student',
        collegeId: user.collegeId,
        departmentId: user.departmentId
      });

      const emailAddress = student.emailId || student.email;
      toast.success('Invitation Sent', `Invitation sent to ${student.name} (${emailAddress})`);
      refetchInvitations();
    } catch (error: any) {
      console.error('Error sending invitation:', error);
      let errorMessage = 'Failed to send invitation';
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }
      toast.error('Send Failed', errorMessage);
    } finally {
      setSendingInvitations(prev => {
        const newSet = new Set(prev);
        newSet.delete(student.id);
        return newSet;
      });
    }
  };

  // Handle canceling invitation
  const handleCancelInvitation = (invitation: Invitation) => {
    setInvitationToCancel(invitation);
    setShowCancelConfirm(true);
  };

  const confirmCancelInvitation = async () => {
    if (!invitationToCancel) return;

    try {
      await cancelInvitationMutation.mutateAsync(invitationToCancel.id);
      toast.success('Success', 'Invitation cancelled successfully');
      refetchInvitations();
    } catch (error) {
      toast.error('Error', 'Failed to cancel invitation');
    } finally {
      setShowCancelConfirm(false);
      setInvitationToCancel(null);
    }
  };

  // Handle resending invitation
  const handleResendInvitation = async (invitation: Invitation) => {
    try {
      await resendInvitationMutation.mutateAsync(invitation.id);
      toast.success('Success', 'Invitation resent successfully');
      refetchInvitations();
    } catch (error) {
      toast.error('Error', 'Failed to resend invitation');
    }
  };

  // Check if student already has a pending invitation
  const getStudentInvitation = (studentEmail: string) => {
    return invitations.find(inv => 
      inv.email === studentEmail && 
      inv.status === 'pending'
    );
  };

  // Get invitation status for display
  const getInvitationStatus = (invitation: Invitation) => {
    switch (invitation.status) {
      case 'pending':
        return { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-50', text: 'Pending' };
      case 'accepted':
        return { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-50', text: 'Accepted' };
      case 'rejected':
        return { icon: XCircle, color: 'text-red-600', bg: 'bg-red-50', text: 'Rejected' };
      case 'expired':
        return { icon: XCircle, color: 'text-gray-600', bg: 'bg-gray-50', text: 'Expired' };
      default:
        return { icon: Clock, color: 'text-gray-600', bg: 'bg-gray-50', text: 'Unknown' };
    }
  };

  if (studentsLoading || invitationsLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-8 h-8 animate-spin text-green-600" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Student Invitations</h1>
        <p className="text-gray-600">Send invitations to students you have added to the system</p>
        <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700">
            <strong>Note:</strong> This screen shows only students that you have personally added to the system.
            You can send login invitations to these students so they can access their accounts.
          </p>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <GraduationCap className="w-5 h-5 mr-2" />
            Students Added by You ({students.length})
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Students you have created and can send invitations to
            {allStudentProfiles.length > students.length && (
              <span className="ml-2 text-xs text-blue-600">
                (Filtered from {allStudentProfiles.length} total department student profiles)
              </span>
            )}
          </p>
        </div>

        <div className="overflow-x-auto">
          {students.length === 0 ? (
            <div className="text-center py-12">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Students Added Yet</h3>
              <p className="text-gray-500 mb-4">
                You haven't added any students to the system yet.
              </p>
              <p className="text-sm text-gray-400">
                Go to <strong>Student Management</strong> to add students first, then return here to send them invitations.
              </p>
            </div>
          ) : (
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 bg-gray-50">
                  <th className="text-left py-3 px-6 font-medium text-gray-700">Student</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">Class</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">Roll Number</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">Status</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {students.map((student) => {
                  const invitation = getStudentInvitation(student.emailId || student.email);
                  const isInvited = !!invitation;
                  const isSending = sendingInvitations.has(student.id);
                  
                  return (
                    <tr key={student.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div>
                          <p className="font-medium text-gray-900">{student.name}</p>
                          <p className="text-sm text-gray-500">{student.emailId || student.email}</p>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-gray-700">{student.class || 'N/A'}</td>
                      <td className="py-4 px-6 text-gray-700">{student.rollNumber || 'N/A'}</td>
                      <td className="py-4 px-6">
                        {isInvited ? (
                          <div className="flex items-center">
                            {(() => {
                              const status = getInvitationStatus(invitation);
                              const StatusIcon = status.icon;
                              return (
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${status.bg} ${status.color}`}>
                                  <StatusIcon className="w-3 h-3 mr-1" />
                                  {status.text}
                                </span>
                              );
                            })()}
                          </div>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                            Not Invited
                          </span>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          {!isInvited ? (
                            <button
                              onClick={() => handleSendInvitation(student)}
                              disabled={isSending}
                              className="bg-green-600 text-white px-3 py-1 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isSending ? (
                                <RefreshCw className="w-3 h-3 animate-spin" />
                              ) : (
                                <Send className="w-3 h-3" />
                              )}
                              <span>{isSending ? 'Sending...' : 'Send Invite'}</span>
                            </button>
                          ) : invitation?.status === 'pending' ? (
                            <>
                              <button
                                onClick={() => handleResendInvitation(invitation)}
                                disabled={resendInvitationMutation.isPending}
                                className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded disabled:opacity-50"
                                title="Resend invitation"
                              >
                                <RefreshCw className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleCancelInvitation(invitation)}
                                disabled={cancelInvitationMutation.isPending}
                                className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded disabled:opacity-50"
                                title="Cancel invitation"
                              >
                                <XCircle className="w-4 h-4" />
                              </button>
                            </>
                          ) : (
                            <span className="text-sm text-gray-500">
                              {invitation?.status === 'accepted' ? 'Joined' : 
                               invitation?.status === 'rejected' ? 'Declined' : 'Expired'}
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Cancel Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showCancelConfirm}
        title="Cancel Invitation"
        message={`Are you sure you want to cancel the invitation sent to ${invitationToCancel?.email}?`}
        confirmText="Cancel Invitation"
        onConfirm={confirmCancelInvitation}
        onCancel={() => {
          setShowCancelConfirm(false);
          setInvitationToCancel(null);
        }}
        isLoading={cancelInvitationMutation.isPending}
      />
    </div>
  );
};

export default StaffInvitationManagement;
