const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

// Database connection
const connectionString = 'postgresql://postgres:postgres%40123@13.201.53.157:5432/treeplantingtest';
const sql = postgres(connectionString);
const db = drizzle(sql);

async function createAdminUser() {
  try {
    console.log('🚀 Creating admin user...');

    // Check if admin user already exists
    const existingAdmin = await sql`
      SELECT id, email FROM treedev.users 
      WHERE email = '<EMAIL>'
    `;

    if (existingAdmin.length > 0) {
      console.log('✅ Admin user already exists:', existingAdmin[0].email);
      return;
    }

    // Get a college to assign to admin
    const colleges = await sql`
      SELECT id, name FROM treedev.colleges 
      LIMIT 1
    `;

    if (colleges.length === 0) {
      console.log('❌ No colleges found. Creating a test college first...');
      
      // Create a test college
      const collegeId = uuidv4();
      await sql`
        INSERT INTO treedev.colleges (id, name, code, address, phone, email, status)
        VALUES (${collegeId}, 'Test College', 'TC001', 'Test Address', '1234567890', '<EMAIL>', 'active')
      `;
      
      console.log('✅ Test college created');
      
      // Use the newly created college
      colleges.push({ id: collegeId, name: 'Test College' });
    }

    const college = colleges[0];
    console.log(`📚 Using college: ${college.name}`);

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Create admin user
    const adminId = uuidv4();
    await sql`
      INSERT INTO treedev.users (id, email, name, password, role, phone, college_id, status, created_at, updated_at)
      VALUES (
        ${adminId}, 
        '<EMAIL>', 
        'System Administrator', 
        ${hashedPassword}, 
        'admin', 
        '9876543210', 
        ${college.id}, 
        'active',
        NOW(),
        NOW()
      )
    `;

    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role: admin');

  } catch (error) {
    console.error('❌ Failed to create admin user:', error);
  } finally {
    await sql.end();
  }
}

// Run the script
createAdminUser();
