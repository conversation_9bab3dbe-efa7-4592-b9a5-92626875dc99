import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { departmentsApi, usersApi } from '../services/api';
import { Department } from '../types';

// Query Keys
export const departmentKeys = {
  all: ['departments'] as const,
  lists: () => [...departmentKeys.all, 'list'] as const,
  list: (filters: Record<string, string> = {}) => [...departmentKeys.lists(), filters] as const,
  details: () => [...departmentKeys.all, 'detail'] as const,
  detail: (id: string) => [...departmentKeys.details(), id] as const,
  stats: (id: string) => [...departmentKeys.all, 'stats', id] as const,
  comparison: (id: string) => [...departmentKeys.detail(id), 'comparison'] as const,
  students: (id: string) => [...departmentKeys.detail(id), 'students'] as const,
};

// Queries
export function useDepartments(params?: Record<string, string>) {
  return useQuery({
    queryKey: departmentKeys.list(params),
    queryFn: () => departmentsApi.getDepartments(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useDepartment(id: string) {
  return useQuery({
    queryKey: departmentKeys.detail(id),
    queryFn: () => departmentsApi.getDepartmentById(id),
    enabled: !!id,
  });
}

export function useDepartmentStats(id: string) {
  return useQuery({
    queryKey: departmentKeys.stats(id),
    queryFn: () => departmentsApi.getDepartmentStats(id),
    enabled: !!id,
  });
}

export function useDepartmentComparison(departmentId?: string) {
  return useQuery({
    queryKey: departmentKeys.comparison(departmentId || ''),
    queryFn: () => departmentsApi.getDepartmentComparison(departmentId!),
    enabled: !!departmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useDepartmentStudents(departmentId?: string) {
  return useQuery({
    queryKey: departmentKeys.students(departmentId || ''),
    queryFn: () => usersApi.getUsers({ departmentId, role: 'student' }),
    enabled: !!departmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Transform user data to student format for the table
      return data.map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        class: user.class || 'N/A',
        semester: user.semester || 'N/A',
        rollNumber: user.rollNumber || 'N/A',
        treesPlanted: user.treePlantingStats?.totalTrees || 0,
        approved: user.treePlantingStats?.approved || 0,
        pending: user.treePlantingStats?.pending || 0,
        rejected: user.treePlantingStats?.rejected || 0,
        completionRate: user.treePlantingStats?.totalTrees > 0
          ? (user.treePlantingStats.approved / user.treePlantingStats.totalTrees) * 100
          : 0,
        lastUpload: user.treePlantingStats?.lastUpload,
      }));
    },
  });
}

// Mutations
export function useCreateDepartment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<Department, 'id' | 'createdAt' | 'updatedAt'>) => 
      departmentsApi.createDepartment(data),
    onSuccess: (newDepartment) => {
      // Invalidate and refetch departments list
      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });
      
      // Optionally add the new department to the cache
      queryClient.setQueryData(departmentKeys.detail(newDepartment.id), newDepartment);
    },
    onError: (error) => {
      console.error('Failed to create department:', error);
    },
  });
}

export function useUpdateDepartment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Department> }) => 
      departmentsApi.updateDepartment(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: departmentKeys.detail(id) });
      await queryClient.cancelQueries({ queryKey: departmentKeys.lists() });

      // Snapshot the previous value
      const previousDepartment = queryClient.getQueryData(departmentKeys.detail(id));
      const previousDepartments = queryClient.getQueryData(departmentKeys.lists());

      // Optimistically update to the new value
      if (previousDepartment) {
        queryClient.setQueryData(departmentKeys.detail(id), { ...previousDepartment, ...data });
      }

      // Update the department in the list
      queryClient.setQueriesData(
        { queryKey: departmentKeys.lists() },
        (oldData: Department[] | undefined) => {
          if (!oldData) return oldData;
          return oldData.map(department => department.id === id ? { ...department, ...data } : department);
        }
      );

      // Return a context object with the snapshotted value
      return { previousDepartment, previousDepartments };
    },
    onError: (err, { id }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousDepartment) {
        queryClient.setQueryData(departmentKeys.detail(id), context.previousDepartment);
      }
      if (context?.previousDepartments) {
        queryClient.setQueryData(departmentKeys.lists(), context.previousDepartments);
      }
      console.error('Failed to update department:', err);
    },
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success to ensure consistency
      queryClient.invalidateQueries({ queryKey: departmentKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });
    },
  });
}

export function useDeleteDepartment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => departmentsApi.deleteDepartment(id),
    onMutate: async (deletedId) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: departmentKeys.lists() });
      await queryClient.cancelQueries({ queryKey: departmentKeys.detail(deletedId) });

      // Snapshot the previous value
      const previousDepartments = queryClient.getQueryData(departmentKeys.lists());

      // Optimistically update to the new value
      queryClient.setQueriesData(
        { queryKey: departmentKeys.lists() },
        (oldData: Department[] | undefined) => {
          if (!oldData) return oldData;
          return oldData.filter(department => department.id !== deletedId);
        }
      );

      // Remove the specific department from cache immediately
      queryClient.removeQueries({ queryKey: departmentKeys.detail(deletedId) });

      // Return a context object with the snapshotted value
      return { previousDepartments };
    },
    onError: (err: any, deletedId, context) => {
      // Check if it's a "not found" error - this might mean the department was already deleted
      const isNotFoundError = err.response?.status === 404 || 
                              err.response?.data?.error === 'Department not found' ||
                              err.message?.includes('not found');
      
      if (isNotFoundError) {
        // If it's a "not found" error, don't roll back - the department is actually deleted
        console.log('Department was already deleted or not found, keeping optimistic update');
        return;
      }
      
      // For other errors, roll back the optimistic update
      if (context?.previousDepartments) {
        queryClient.setQueryData(departmentKeys.lists(), context.previousDepartments);
      }
      console.error('Failed to delete department:', err);
    },
    onSettled: (_, __, deletedId) => {
      // Always refetch after error or success to ensure consistency
      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });
      // Don't refetch the deleted department detail as it should be gone
    },
  });
}
